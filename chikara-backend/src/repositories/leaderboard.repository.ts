import gameConfig from "../config/gameConfig.js";
import { db } from "../lib/db.js";

export const getLevelLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
        },
        orderBy: [{ level: "desc" }, { xp: "desc" }],
        select: {
            id: true,
            username: true,
            avatar: true,
            level: true,
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getPvpWinsLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    battleWins: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                battleWins: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    battleWins: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getMoneyLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
        },
        orderBy: {
            bank_balance: "desc",
        },
        select: {
            id: true,
            username: true,
            avatar: true,
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getStrengthLeaderboard = async () => {
    return await db.user_skill
        .findMany({
            where: {
                skillType: "strength",
                user: {
                    NOT: {
                        userType: "admin",
                    },
                },
            },
            orderBy: {
                level: "desc",
            },
            select: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
            take: gameConfig.USERS_PER_BOARD,
        })
        .then((results) => results.map((r) => r.user));
};

export const getDefenceLeaderboard = async () => {
    return await db.user_skill
        .findMany({
            where: {
                skillType: "defence",
                user: {
                    NOT: {
                        userType: "admin",
                    },
                },
            },
            orderBy: {
                level: "desc",
            },
            select: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
            take: gameConfig.USERS_PER_BOARD,
        })
        .then((results) => results.map((r) => r.user));
};

export const getIntelligenceLeaderboard = async () => {
    return await db.user_skill
        .findMany({
            where: {
                skillType: "intelligence",
                user: {
                    NOT: {
                        userType: "admin",
                    },
                },
            },
            orderBy: {
                level: "desc",
            },
            select: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
            take: gameConfig.USERS_PER_BOARD,
        })
        .then((results) => results.map((r) => r.user));
};

export const getVitalityLeaderboard = async () => {
    return await db.user_skill
        .findMany({
            where: {
                skillType: "vitality",
                user: {
                    NOT: {
                        userType: "admin",
                    },
                },
            },
            orderBy: {
                level: "desc",
            },
            select: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
            take: gameConfig.USERS_PER_BOARD,
        })
        .then((results) => results.map((r) => r.user));
};

export const getDexterityLeaderboard = async () => {
    return await db.user_skill
        .findMany({
            where: {
                skillType: "dexterity",
                user: {
                    NOT: {
                        userType: "admin",
                    },
                },
            },
            orderBy: {
                level: "desc",
            },
            select: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
            take: gameConfig.USERS_PER_BOARD,
        })
        .then((results) => results.map((r) => r.user));
};

export const getEnduranceLeaderboard = async () => {
    return await db.user_skill
        .findMany({
            where: {
                skillType: "endurance",
                user: {
                    NOT: {
                        userType: "admin",
                    },
                },
            },
            orderBy: {
                level: "desc",
            },
            select: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
            take: gameConfig.USERS_PER_BOARD,
        })
        .then((results) => results.map((r) => r.user));
};

export const getZonesLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    roguelikeMapsCompleted: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                roguelikeMapsCompleted: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    roguelikeMapsCompleted: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getJobLevelLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
        },
        orderBy: {
            jobLevel: "desc",
        },
        select: {
            id: true,
            username: true,
            avatar: true,
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getCraftsLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    craftsCompleted: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                craftsCompleted: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    craftsCompleted: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getNpcWinsLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    npcBattleWins: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                npcBattleWins: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    npcBattleWins: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getQuestsLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    questsCompleted: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                questsCompleted: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    questsCompleted: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getMuggingGainLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    totalMuggingGain: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                totalMuggingGain: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    totalMuggingGain: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getMuggingLossLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    totalMuggingLoss: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                totalMuggingLoss: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    totalMuggingLoss: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getCasinoWinnerLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    totalCasinoProfitLoss: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                totalCasinoProfitLoss: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    totalCasinoProfitLoss: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getCasinoLoserLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    totalCasinoProfitLoss: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                totalCasinoProfitLoss: "asc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    totalCasinoProfitLoss: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getTotalMissionHoursLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    totalMissionHours: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                totalMissionHours: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    totalMissionHours: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getTotalStatsLeaderboard = async () => {
    // Get users with their skill levels
    const users = await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_skills: {
                where: {
                    skillType: {
                        in: ["strength", "defence", "dexterity", "intelligence", "endurance", "vitality"],
                    },
                },
                select: {
                    skillType: true,
                    level: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD * 2, // Get more to ensure we have enough after filtering
    });

    // Transform to include stat totals
    return users.map((user) => {
        const stats = {
            strength: 1,
            defence: 1,
            dexterity: 1,
            intelligence: 1,
            endurance: 1,
            vitality: 1,
        };

        for (const skill of user.user_skills) {
            stats[skill.skillType as keyof typeof stats] = skill.level;
        }

        return {
            id: user.id,
            username: user.username,
            avatar: user.avatar,
            strength: stats.strength,
            defence: stats.defence,
            dexterity: stats.dexterity,
            intelligence: stats.intelligence,
            endurance: stats.endurance,
            vitality: stats.vitality,
        };
    });
};

export const getTotalBountyRewardsLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    totalBountyRewards: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                totalBountyRewards: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    totalBountyRewards: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getMarketItemsSoldLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    marketItemsSold: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                marketItemsSold: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    marketItemsSold: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export const getMarketMoneyMadeLeaderboard = async () => {
    return await db.user.findMany({
        where: {
            NOT: {
                userType: "admin",
            },
            user_achievements: {
                is: {
                    marketMoneyMade: {
                        not: null,
                    },
                },
            },
        },
        orderBy: {
            user_achievements: {
                marketMoneyMade: "desc",
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            user_achievements: {
                select: {
                    marketMoneyMade: true,
                },
            },
        },
        take: gameConfig.USERS_PER_BOARD,
    });
};

export default {
    getLevelLeaderboard,
    getPvpWinsLeaderboard,
    getMoneyLeaderboard,
    getStrengthLeaderboard,
    getDefenceLeaderboard,
    getIntelligenceLeaderboard,
    getVitalityLeaderboard,
    getDexterityLeaderboard,
    getEnduranceLeaderboard,
    getZonesLeaderboard,
    getJobLevelLeaderboard,
    getCraftsLeaderboard,
    getNpcWinsLeaderboard,
    getQuestsLeaderboard,
    getMuggingGainLeaderboard,
    getMuggingLossLeaderboard,
    getCasinoWinnerLeaderboard,
    getCasinoLoserLeaderboard,
    getTotalMissionHoursLeaderboard,
    getTotalStatsLeaderboard,
    getTotalBountyRewardsLeaderboard,
    getMarketItemsSoldLeaderboard,
    getMarketMoneyMadeLeaderboard,
};
