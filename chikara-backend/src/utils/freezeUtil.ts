export default function deepFreeze<T extends object>(object: T): T {
    Object.freeze(object);

    for (const prop of Object.getOwnPropertyNames(object)) {
        if (Object.prototype.hasOwnProperty.call(object, prop)) {
            const value = (object as Record<string, unknown>)[prop];
            if (
                value !== null &&
                (typeof value === "object" || typeof value === "function") &&
                !Object.isFrozen(value)
            ) {
                deepFreeze(value as object);
            }
        }
    }

    return object;
}
