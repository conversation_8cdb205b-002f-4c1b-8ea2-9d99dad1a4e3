import arrow from "@/assets/images/UI/arrow.gif";
import NotificationBadge from "@/components/NotificationBadge";
import useGetAvailableQuestList from "@/features/tasks/api/useGetAvailableQuestList";
import { mobileNavItems } from "@/helpers/navItems";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { Link, useLocation, Location } from "react-router-dom";
import { useNormalStore } from "../../app/store/stores";

const EXCLUDED_PAGES = ["/fight"];

interface NavButtonProps {
    url: string;
    text: string;
    image: string;
    current: boolean;
    isIncapacitated: boolean;
    inFight?: string | null;
    availableQuests?: number;
    unreadChatMessages: number;
    displayTutorialArrow?: boolean;
    isDisabled: boolean;
    craftCollectReady: boolean;
    location: Location;
}

const NavButton = ({
    url,
    text,
    image,
    current,
    isIncapacitated,
    inFight,
    availableQuests,
    unreadChatMessages,
    displayTutorialArrow = false,
    isDisabled,
    craftCollectReady,
}: NavButtonProps) => {
    let disabled = isDisabled;
    if (isIncapacitated) {
        if (text === "Explore" || text === "Adventure") {
            disabled = true;
        }
    }
    if (inFight) {
        disabled = true;
    }
    const tutorialArrow = text === "Tasks" && !current && displayTutorialArrow;

    const link = disabled ? "#" : url;

    // Map nav items to gradient colors for gamified look
    const getButtonGradient = (name: string) => {
        switch (name) {
            case "Home":
                return "from-primary to-primary/70";
            case "Campus":
                return "from-secondary to-secondary/70";
            case "Explore":
                return "from-accent to-accent/70";
            case "Tasks":
                return "from-warning to-warning/70";
            case "Chat":
                return "from-info to-info/70";
            case "Hospital":
                return "from-error to-error/70";
            case "Jail":
                return "from-neutral to-neutral/70";
            default:
                return "from-primary to-primary/70";
        }
    };

    const getIconBg = (name: string) => {
        switch (name) {
            case "Home":
                return "bg-primary/20";
            case "Campus":
                return "bg-secondary/20";
            case "Explore":
                return "bg-accent/20";
            case "Tasks":
                return "bg-warning/20";
            case "Chat":
                return "bg-info/20";
            case "Hospital":
                return "bg-error/20";
            case "Jail":
                return "bg-neutral/20";
            default:
                return "bg-primary/20";
        }
    };

    return (
        <Link to={link} className="relative group">
            {tutorialArrow && (
                <img
                    className="-rotate-90 -translate-x-1/2 -top-20 absolute left-1/2 size-20 z-50"
                    src={arrow}
                    alt=""
                />
            )}
            <button
                type="button"
                className={cn(
                    "relative flex flex-col items-center justify-center p-1.5 rounded-xl",
                    "transition-all duration-200 transform",
                    "active:scale-95",
                    current && "scale-110",
                    disabled && "opacity-50 cursor-not-allowed"
                )}
                disabled={disabled}
            >
                {/* Glow effect for active button */}
                {current && (
                    <div className={cn(
                        "absolute inset-0 rounded-xl bg-gradient-to-t blur-lg opacity-50 -z-10",
                        getButtonGradient(text)
                    )} />
                )}
                
                {/* Icon container with gamified styling */}
                <div className={cn(
                    "relative p-2 rounded-xl transition-all duration-200",
                    "bg-gradient-to-br",
                    getButtonGradient(text),
                    current ? "shadow-lg ring-2 ring-white/25" : "shadow-md",
                    disabled && "grayscale"
                )}>
                    {/* Notification badges */}
                    {text === "Explore" && craftCollectReady && (
                        <span className="absolute -top-1 -right-1 z-10">
                            <span className="relative flex h-2.5 w-2.5">
                                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-error opacity-75"></span>
                                <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-error"></span>
                            </span>
                        </span>
                    )}

                    {text === "Tasks" && availableQuests ? (
                        <span className="absolute -top-1.5 -right-1.5 z-10">
                            <span className="badge badge-error badge-xs px-1 min-w-[1rem] h-4 text-[10px] font-bold shadow-sm">
                                {availableQuests > 9 ? "9+" : availableQuests}
                            </span>
                        </span>
                    ) : null}

                    {text === "Chat" && unreadChatMessages > 0 && (
                        <span className="absolute -top-1.5 -right-1.5 z-10">
                            <span className="badge badge-error badge-xs px-1 min-w-[1rem] h-4 text-[10px] font-bold shadow-sm">
                                {unreadChatMessages > 99 ? "99+" : unreadChatMessages}
                            </span>
                        </span>
                    )}
                    
                    {/* Icon */}
                    <img
                        src={image}
                        alt={text}
                        className={cn(
                            "h-6 w-6 transition-all duration-200",
                            current && "drop-shadow-[0_0_8px_rgba(255,255,255,0.8)]"
                        )}
                    />
                </div>
                
                {/* Text label */}
                <span className={cn(
                    "mt-0.5 text-[10px] font-bold transition-all duration-200",
                    "text-base-content",
                    current ? "opacity-100" : "opacity-75"
                )}>
                    {text}
                </span>
            </button>
        </Link>
    );
};

export default function MobileBottomNav() {
    const location = useLocation();
    const { data: availableQuests } = useGetAvailableQuestList();
    const { data: currentUser } = useFetchCurrentUser();
    const { unreadChatMessages, craftCollectReady, preventNavigation } = useNormalStore();

    const hospitalised = (currentUser?.hospitalisedUntil ?? 0) > 0;
    const jailed = (currentUser?.jailedUntil ?? 0) > 0;

    const navigation = mobileNavItems(hospitalised, jailed);

    if (EXCLUDED_PAGES.includes(location.pathname)) return null;

    return (
        <div className="fixed bottom-0 left-0 right-0 z-50 px-2 pb-safe">
            {/* Dark theme matching background */}
            <div className="relative bg-[#1a1d29] rounded-t-2xl border-t border-primary/20 shadow-[0_-4px_24px_rgba(0,0,0,0.8)] overflow-hidden">
                {/* Subtle gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-base-100/20 to-transparent opacity-50" />
                
                {/* Top accent line */}
                <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent" />
                
                {/* Main nav container with tighter spacing */}
                <div className="relative flex items-center justify-center px-2 py-1.5 gap-2">
                    {navigation.map((navItem) => (
                        <Fragment key={navItem.name}>
                            <NavButton
                                isDisabled={preventNavigation}
                                url={navItem.href}
                                text={navItem.name}
                                image={navItem.icon}
                                current={navItem.href === location.pathname}
                                isIncapacitated={hospitalised || jailed}
                                inFight={currentUser?.battleValidUntil}
                                availableQuests={availableQuests?.data?.length}
                                unreadChatMessages={unreadChatMessages}
                                location={location}
                                craftCollectReady={craftCollectReady}
                            />
                        </Fragment>
                    ))}
                </div>
            </div>
        </div>
    );
}
