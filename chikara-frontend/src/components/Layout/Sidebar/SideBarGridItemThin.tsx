import arrow from "@/assets/images/UI/arrow.gif";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router-dom";
import { useNormalStore } from "../../../app/store/stores";

interface NavItem {
    name: string;
    href: string;
    icon: string;
    current?: string;
    external?: boolean;
    thin?: boolean;
    construction?: boolean;
}

interface SideBarGridItemThinProps {
    item: NavItem;
    inFight?: string | null;
    availableQuests?: number;
}

interface ButtonContentProps {
    item: NavItem;
    inFight?: string | null;
    checkCurrent: (name?: string) => boolean;
    availableQuests?: number;
    displayTutorialArrow?: boolean;
    craftCollectReady?: boolean;
}

const ButtonContent = ({
    item,
    inFight,
    checkCurrent,
    availableQuests,
    displayTutorialArrow = false,
    craftCollectReady,
}: ButtonContentProps) => {
    const tutorialArrow = item.name === "Tasks" && !checkCurrent(item.current) && displayTutorialArrow;

    return (
        <div className="relative w-full h-full flex items-center justify-center min-w-0 px-3">
            {tutorialArrow && (
                <img
                    className="-rotate-90 -translate-x-1/2 -top-20 absolute left-1/2 z-100 size-24 scale-75"
                    src={arrow}
                    alt=""
                />
            )}

            {/* Notification badges container */}
            <div className="absolute inset-0 pointer-events-none">
                {item.name === "Campus" && craftCollectReady && (
                    <div className="indicator-item indicator-end indicator-top badge badge-secondary badge-sm animate-pulse shadow-lg shadow-secondary/50">
                        <span className="loading loading-ring loading-xs"></span>
                    </div>
                )}

                {item.name === "Tasks" && availableQuests ? (
                    <div className="indicator-item indicator-end indicator-top badge badge-error badge-sm font-bold shadow-lg shadow-error/50 animate-bounce">
                        {availableQuests}
                    </div>
                ) : null}
            </div>

            {/* Icon container with background */}
            <div className="avatar mr-2">
                <div className={cn(
                    "w-8 h-8 rounded-lg p-1.5 transition-all",
                    checkCurrent(item.current) 
                        ? "bg-accent/20 ring-2 ring-accent" 
                        : "bg-base-100/20 group-hover:bg-primary/20"
                )}>
                    <img
                        src={item.icon}
                        alt=""
                        className={cn(
                            "w-full h-full object-contain transition-transform group-hover:scale-110",
                            (item.construction || inFight) && "brightness-50 opacity-50"
                        )}
                    />
                </div>
            </div>

            {/* Text container */}
            <div className="flex-1 min-w-0">
                <span 
                    className={cn(
                        "font-bold text-sm uppercase tracking-wide transition-all",
                        item.construction && "opacity-50",
                        checkCurrent(item.current) 
                            ? "text-accent-content drop-shadow-lg" 
                            : "text-primary-content group-hover:drop-shadow-lg"
                    )}
                >
                    {item.name}
                </span>
            </div>
        </div>
    );
};

export default function SideBarGridItemThin({ item, inFight, availableQuests }: SideBarGridItemThinProps) {
    const location = useLocation();
    const { craftCollectReady } = useNormalStore();

    const checkCurrent = (name?: string): boolean => {
        if (!name) return false;
        return `/${name}` === location.pathname;
    };

    if (item.external) {
        return (
            <a
                href={item.href}
                target="_blank"
                rel="noreferrer"
                className={cn(
                    "btn btn-secondary btn-outline",
                    "group relative col-span-2 flex w-full h-12 min-h-0 rounded-xl text-center font-medium text-xs",
                    "transition-all duration-200 hover:scale-105 hover:btn-secondary hover:shadow-lg hover:shadow-secondary/30"
                )}
            >
                <ButtonContent item={item} inFight={inFight} checkCurrent={checkCurrent} />
            </a>
        );
    }

    return (
        <NavLink
            to={!inFight ? item.href : "#"}
            aria-current={checkCurrent(item.current) ? "page" : undefined}
            className={cn(
                "btn",
                checkCurrent(item.current) 
                    ? "btn-accent shadow-xl shadow-accent/30 ring-2 ring-accent/50" 
                    : "btn-primary btn-outline hover:btn-primary hover:shadow-lg hover:shadow-primary/30",
                "group relative col-span-2 flex w-full h-12 min-h-0 rounded-xl text-center font-medium text-xs",
                "transition-all duration-200 hover:scale-105",
                inFight && "btn-disabled opacity-50"
            )}
        >
            <ButtonContent
                item={item}
                inFight={inFight}
                checkCurrent={checkCurrent}
                availableQuests={availableQuests}
                craftCollectReady={craftCollectReady}
            />
        </NavLink>
    );
}
