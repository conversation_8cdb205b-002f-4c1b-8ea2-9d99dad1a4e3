import apImg from "@/assets/icons/UI/APicon3.png";
import hpImg from "@/assets/icons/UI/HPicon.png";
import energyImg from "@/assets/icons/UI/energyicon.png";
import expImg from "@/assets/icons/UI/expBG.png";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { CountdownTimer } from "../CountdownTimer";
import type { User } from "@/types/user";

type StatBarType = "health" | "energy" | "actionPoints" | "exp";

interface StatBarTypeConfig {
    fill: string;
    iconSize: string;
    image: string;
    alt: string;
}

interface SidebarStatBarProps {
    barPercentage: string;
    type: StatBarType;
    barText?: string;
    currentUser?: User;
    onClick?: () => void;
    nextTick?: number;
    isMax?: boolean;
    tooltipTitle?: string;
    tooltipDescription?: string;
}

export default function SidebarStatBar({
    barPercentage,
    type,
    barText,
    currentUser,
    onClick,
    nextTick,
    isMax,
    tooltipTitle,
    tooltipDescription,
}: SidebarStatBarProps) {
    const statBarTypes: Record<Exclude<StatBarType, "exp">, StatBarTypeConfig> = {
        health: {
            fill: "greenSliderFill",
            iconSize: "2xl:h-6 h-5 w-auto 2xl:left-[12%] left-5",
            image: hpImg,
            alt: "Health Bar",
        },
        energy: {
            fill: "yellowSliderFill",
            iconSize: "2xl:h-6 h-5 w-auto 2xl:left-[15%] left-6",
            image: energyImg,
            alt: "Energy Bar",
        },
        actionPoints: {
            fill: "blueSliderFill",
            iconSize: "2xl:h-6 h-5 w-auto 2xl:left-[13%] left-6",
            image: apImg,
            alt: "Action Points Bar",
        },
    };

    const { MAX_LEVEL_CAP } = useGameConfig();
    const isMaxLevel = (currentUser?.level ?? 0) >= MAX_LEVEL_CAP;

    const barPercentString = parseFloat(barPercentage) * 112.1 + "%";

    const variableBorderSlice = `${parseFloat(barPercentage) * (type === "exp" ? 400 : 375)}% fill`;
    const borderSlice = parseFloat(barPercentage) < 0.13 ? variableBorderSlice : "49% fill";

    // EXP BAR //
    if (type === "exp") {
        const expBarPercent = isMaxLevel ? 1 : parseFloat(barPercentage);
        const expBarText = isMaxLevel ? "MAX" : `${barText}`;
        return (
            <div className="relative w-full">
                {/* Username and ID */}
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                        <div className="avatar">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center shadow-lg shadow-primary/30">
                                <span className="text-primary-content font-bold text-sm">{currentUser?.level}</span>
                            </div>
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="font-bold text-sm text-primary truncate">{currentUser?.username}</h3>
                            <p className="text-xs text-base-content/60">#{currentUser?.id}</p>
                        </div>
                    </div>
                    <div className="badge badge-accent badge-sm font-bold flex-shrink-0">LVL {currentUser?.level}</div>
                </div>
                
                {/* Progress bar */}
                <div className="relative">
                    <progress 
                        className="progress progress-accent w-full h-6 shadow-lg shadow-accent/20" 
                        value={expBarPercent * 100} 
                        max="100"
                    ></progress>
                    <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-bold text-accent-content drop-shadow-lg">{expBarText}</span>
                    </div>
                </div>
            </div>
        );
    }

    const progressColors = {
        health: "progress-success",
        energy: "progress-warning",
        actionPoints: "progress-info",
    };

    const shadowColors = {
        health: "shadow-success/20",
        energy: "shadow-warning/20",
        actionPoints: "shadow-info/20",
    };

    return (
        <div className="relative mb-2">
            <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-1">
                    <div className="avatar">
                        <div className="w-6 h-6 rounded bg-base-100/30 p-1">
                            <img
                                src={type ? statBarTypes[type as keyof typeof statBarTypes].image : ""}
                                alt={type ? statBarTypes[type as keyof typeof statBarTypes].alt : ""}
                                className="w-full h-full object-contain"
                            />
                        </div>
                    </div>
                    <span className="text-xs font-bold text-base-content/80">
                        {tooltipTitle?.replace("(", "").replace(")", "").toUpperCase()}
                    </span>
                </div>
                {nextTick && !isMax && !isNaN(nextTick) && (
                    <span className="text-xs font-medium text-primary">
                        <CountdownTimer targetDate={nextTick} />
                    </span>
                )}
            </div>
            
            <div className="relative">
                <progress 
                    className={cn(
                        "progress w-full h-5 shadow-lg",
                        progressColors[type],
                        shadowColors[type]
                    )} 
                    value={parseFloat(barPercentage) * 100} 
                    max="100"
                ></progress>
                <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-bold text-base-100 drop-shadow-lg">{barText}</span>
                </div>
            </div>
        </div>
    );
}
