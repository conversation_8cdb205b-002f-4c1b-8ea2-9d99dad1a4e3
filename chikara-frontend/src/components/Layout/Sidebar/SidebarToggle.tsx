import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface SidebarToggleProps {
    isCollapsed: boolean;
    onToggle: () => void;
}

export default function SidebarToggle({ isCollapsed, onToggle }: SidebarToggleProps) {
    return (
        <button
            type="button"
            title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            aria-expanded={!isCollapsed}
            className={cn(
                "btn btn-circle btn-primary btn-xs",
                "h-7 w-7 min-h-0",
                "shadow-lg hover:shadow-primary/50",
                "transition-all hover:scale-110"
            )}
            onClick={onToggle}
        >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </button>
    );
}
