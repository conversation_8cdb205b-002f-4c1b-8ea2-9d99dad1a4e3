import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Link } from "react-router-dom";
import { User } from "lucide-react";

interface BankTransferProps {
    transaction: {
        initiatorId: string;
        secondPartyId: string;
    };
    currentUser: string;
}

export default function BankTransfer({ transaction, currentUser }: BankTransferProps) {
    let secondUser;
    if (transaction.initiatorId === currentUser) {
        secondUser = transaction.secondPartyId;
    } else {
        secondUser = transaction.initiatorId;
    }

    const { data: user } = useGetUserInfo(secondUser, {
        enabled: !!secondUser,
    });

    const isRecipient = transaction.initiatorId !== currentUser;

    return (
        <div className="flex items-center gap-2 text-sm">
            <span className="text-gray-500">
                {isRecipient ? "From" : "To"}
            </span>
            <Link 
                to={`/profile/${secondUser}`} 
                className="link link-hover text-blue-400 font-medium flex items-center gap-1 hover:text-blue-300"
            >
                <User className="w-3 h-3" />
                {user?.username || `#${secondUser}`}
            </Link>
        </div>
    );
}
