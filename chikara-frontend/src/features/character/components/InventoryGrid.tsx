import { DisplayItem } from "@/components/DisplayItem";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { useState, useMemo } from "react";
import useGetInventory from "@/hooks/api/useGetInventory";
import type { InventoryItem } from "@/types/item";
import { ItemRarities } from "@/types/item";
import { Search, X, Package, Sword, Shield, Gem, Archive } from "lucide-react";

interface EquippedItems {
    [key: string]: {
        userItemId: number;
        id: number;
    };
}

interface InventoryGridProps {
    equippedItems: EquippedItems;
    currentUser: any;
}

// Rarity order for sorting
const RARITY_ORDER = {
    [ItemRarities.legendary]: 6,
    [ItemRarities.military]: 5,
    [ItemRarities.specialist]: 4,
    [ItemRarities.enhanced]: 3,
    [ItemRarities.standard]: 2,
    [ItemRarities.novice]: 1,
};

function InventoryGrid({ equippedItems, currentUser }: InventoryGridProps) {
    const isMobile = useCheckMobileScreen();
    const { isLoading, error, data } = useGetInventory();
    const [selectedTab, setSelectedTab] = useState<string>("All");
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

    const itemTypeFilters: Record<string, { types: string[]; icon: React.ElementType; color: string }> = {
        All: { types: [], icon: Package, color: "text-primary" },
        Weapons: { types: ["weapon", "ranged", "offhand"], icon: Sword, color: "text-error" },
        Armor: {
            types: ["head", "chest", "hands", "legs", "feet", "finger", "shield"],
            icon: Shield,
            color: "text-info",
        },
        Consumables: { types: ["consumable", "recipe", "pet"], icon: Gem, color: "text-success" },
        Material: { types: ["crafting", "upgrade"], icon: Gem, color: "text-warning" },
        Misc: { types: ["special", "quest", "junk"], icon: Archive, color: "text-secondary" },
    };

    const displayItemType = (itemType: string): string => {
        if (itemType === "weapon") return "Melee Weapon";
        if (itemType === "ranged") return "Ranged Weapon";
        if (itemType === "quest") return "Task Item";
        if (itemType === "crafting") return "Material";
        if (itemType === "special") return "Special Item";
        return capitaliseFirstLetter(itemType);
    };

    // Filter and sort items
    const filteredItems = useMemo(() => {
        if (!data) return [];

        let items = [...data];

        // Apply tab filter
        if (selectedTab !== "All") {
            const allowedTypes = itemTypeFilters[selectedTab].types;
            items = items.filter((item) => allowedTypes.includes(item.item.itemType));
        }

        // Apply search filter
        if (searchQuery) {
            items = items.filter((item) => item.item.name.toLowerCase().includes(searchQuery.toLowerCase()));
        }

        // Sort by rarity (highest first), then by level, then by name
        items.sort((a, b) => {
            const rarityA = RARITY_ORDER[a.item.rarity as keyof typeof RARITY_ORDER] || 0;
            const rarityB = RARITY_ORDER[b.item.rarity as keyof typeof RARITY_ORDER] || 0;

            if (rarityA !== rarityB) return rarityB - rarityA;
            if (a.item.level !== b.item.level) return (b.item.level || 0) - (a.item.level || 0);
            return a.item.name.localeCompare(b.item.name);
        });

        return items;
    }, [data, selectedTab, searchQuery]);

    const ItemCard = ({ userItem }: { userItem: InventoryItem }) => {
        const { item } = userItem;
        const isEquipped = equippedItems?.[item.itemType]?.userItemId === userItem.id;
        const canEquip = currentUser?.level && item.level ? currentUser.level >= item.level : false;
        const isSelected = selectedItem?.id === userItem.id;

        // Get rarity-based styling
        const getRarityClass = (rarity: string) => {
            switch (rarity) {
                case ItemRarities.legendary:
                    return "hover:shadow-warning hover:shadow-lg";
                case ItemRarities.military:
                    return "hover:shadow-secondary hover:shadow-lg";
                case ItemRarities.specialist:
                    return "hover:shadow-primary hover:shadow-lg";
                case ItemRarities.enhanced:
                    return "hover:shadow-info hover:shadow-lg";
                case ItemRarities.standard:
                    return "hover:shadow-success hover:shadow-lg";
                default:
                    return "hover:shadow-lg";
            }
        };

        return (
            <div
                className={cn(
                    "card card-compact bg-base-200 cursor-pointer transition-all duration-300 aspect-square relative overflow-hidden",
                    isSelected && "ring-2 ring-primary shadow-xl",
                    "hover:scale-110 hover:z-10",
                    getRarityClass(item.rarity)
                )}
                onClick={() => setSelectedItem(userItem)}
            >
                {/* Item Image Container */}
                <figure className="relative h-full w-full flex items-center justify-center bg-base-300">
                    <DisplayItem item={userItem} className="h-full w-full object-contain" itemTypeFrame />

                    {/* Quantity badge */}
                    {userItem.count > 1 && (
                        <div className="badge badge-neutral badge-sm absolute bottom-1 right-1">{userItem.count}</div>
                    )}

                    {/* Equipped indicator */}
                    {isEquipped && (
                        <div className="indicator-item indicator-top indicator-start absolute top-1 left-1">
                            <span className="badge badge-warning badge-sm font-bold">E</span>
                        </div>
                    )}

                    {/* Level requirement indicator - only show if can't equip */}
                    {item.level && !canEquip && (
                        <div className="badge badge-error badge-sm absolute top-1 right-1">Lv.{item.level}</div>
                    )}

                    {/* Upgrade level */}
                    {userItem.upgradeLevel > 0 && (
                        <div className="badge badge-success badge-sm absolute bottom-1 left-1">
                            +{userItem.upgradeLevel}
                        </div>
                    )}
                </figure>
            </div>
        );
    };

    if (error) return "An error has occurred: " + error.message;

    return (
        <div className="flex flex-col h-full bg-base-100">
            {/* Header with tabs and search */}
            <div className="mb-4 space-y-4">
                {/* Tabs */}
                <InventoryTabs
                    selectedTab={selectedTab}
                    setSelectedTab={setSelectedTab}
                    itemTypeFilters={itemTypeFilters}
                />

                {/* Search bar with item count */}
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 px-2 md:px-0">
                    <label className="input input-bordered flex items-center gap-2 flex-1 bg-base-200 border-base-300">
                        <Search className="w-4 h-4 opacity-70" />
                        <input
                            type="text"
                            className="grow"
                            placeholder="Search items..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                        {searchQuery && (
                            <button onClick={() => setSearchQuery("")} className="btn btn-ghost btn-xs btn-circle">
                                <X className="w-4 h-4" />
                            </button>
                        )}
                    </label>
                    {/* Item count badge */}
                    <div className="badge badge-lg badge-neutral self-center sm:self-auto">
                        <Package className="w-3 h-3 mr-1" />
                        {filteredItems.length} {filteredItems.length === 1 ? "item" : "items"}
                    </div>
                </div>
            </div>

            {/* Inventory grid */}
            <div className="flex-1 overflow-y-auto px-2 md:px-0">
                {isLoading ? (
                    <div className="flex flex-col items-center justify-center h-64 gap-4">
                        <span className="loading loading-spinner loading-lg text-primary"></span>
                        <p className="text-base-content/60">Loading inventory...</p>
                    </div>
                ) : filteredItems.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-64">
                        <div className="text-center space-y-2">
                            <Package className="w-16 h-16 mx-auto text-base-content/20" />
                            <p className="text-base-content/60">
                                {searchQuery ? "No items match your search" : "No items in this category"}
                            </p>
                        </div>
                    </div>
                ) : (
                    <div
                        className={cn(
                            "grid gap-3 pb-4",
                            isMobile
                                ? "grid-cols-3 sm:grid-cols-4"
                                : "grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10"
                        )}
                    >
                        {filteredItems.map((userItem) => (
                            <ItemCard key={userItem.id} userItem={userItem} />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}

interface InventoryTabsProps {
    selectedTab: string;
    setSelectedTab: (tab: string) => void;
    itemTypeFilters: Record<string, { types: string[]; icon: React.ElementType; color: string }>;
}

const InventoryTabs = ({ selectedTab, setSelectedTab, itemTypeFilters }: InventoryTabsProps) => {
    const tabs = Object.keys(itemTypeFilters);

    return (
        <div className="px-2 md:px-0">
            {/* Mobile select dropdown */}
            <div className="md:hidden">
                <select
                    className="select select-bordered w-full bg-base-200"
                    value={selectedTab}
                    onChange={(e) => setSelectedTab(e.target.value)}
                >
                    {tabs.map((tab) => {
                        const Icon = itemTypeFilters[tab].icon;
                        return (
                            <option key={tab} value={tab}>
                                {tab}
                            </option>
                        );
                    })}
                </select>
            </div>

            {/* Desktop tabs */}
            <div className="hidden md:block">
                <div role="tablist" className="tabs tabs-boxed bg-base-200 p-1">
                    {tabs.map((tab) => {
                        const { icon: Icon, color } = itemTypeFilters[tab];
                        return (
                            <button
                                key={tab}
                                role="tab"
                                className={cn(
                                    "tab gap-2 transition-all",
                                    selectedTab === tab ? "tab-active bg-base-300" : "hover:bg-base-300/50"
                                )}
                                onClick={() => setSelectedTab(tab)}
                            >
                                <Icon className={cn("w-4 h-4", selectedTab === tab ? color : "opacity-50")} />
                                <span className={cn(selectedTab === tab && "font-semibold")}>{tab}</span>
                            </button>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default InventoryGrid;
