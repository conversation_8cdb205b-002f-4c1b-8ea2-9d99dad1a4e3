import ErrorBoundary from "@/components/Layout/ErrorBoundary";
import Spinner from "@/components/Spinners/Spinner";
import useGetChatHistory from "@/features/chat/api/useGetChatHistory";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { Fragment, useEffect, useLayoutEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { useNormalStore, usePersistStore, useSessionStore, useSocketStore } from "../../app/store/stores";
import ChatMessage from "./components/ChatMessage";
import ChatTextAreaInput from "./components/ChatTextAreaInput";
import ChatTopPanel from "./components/ChatTopPanel";
import ConsoleMessage from "./components/ConsoleMessage";
import type { ChatMessage as ChatMessageType, ChatRoom } from "./types/chat";

interface ChatboxProps {
    isMainChat?: boolean;
    fullSize?: boolean;
    chatRoom?: ChatRoom["room"];
    hideHeader?: boolean;
    customHeight?: string | null;
}

const Chatbox = ({
    isMainChat = false,
    fullSize = false,
    chatRoom = { id: 1, name: "global" },
    hideHeader = false,
    customHeight = null,
}: ChatboxProps) => {
    const { isSocketConnected, socket } = useSocketStore();
    const [newMessagesBox, setNewMessagesBox] = useState<boolean>(false);
    const [focusChatMsgInput, setFocusChatMsgInput] = useState<boolean>(false);
    const [selectedReplyMessage, setSelectedReplyMessage] = useState<ChatMessageType | null>(null);
    const { muteChat, setMuteChat, hideGlobalChat } = usePersistStore();
    const { hideChat, setHideChat, mainChatRoom } = useSessionStore();

    const isChatHidden = hideChat || hideGlobalChat;

    const { resetUnreadChatMessages } = useNormalStore();
    const location = useLocation();
    const chatConfig = useGameConfig();

    let room: ChatRoom["room"];
    if (isMainChat) {
        room = mainChatRoom.room;
    } else {
        room = chatRoom;
    }

    const { data: currentUser, isLoading: userIsLoading } = useFetchCurrentUser();
    const { data: chatMessages, isLoading } = useGetChatHistory(
        {
            roomId: room.id,
            limit: 200,
        },
        {
            staleTime: Number.POSITIVE_INFINITY,
            select: (data: ChatMessageType[]) => [...data].reverse(),
        }
    );
    const isMobile = useCheckMobileScreen();

    const messagesEndRef = useRef<HTMLDivElement>(null);
    const messageWindowRef = useRef<HTMLDivElement>(null);

    const triggerScroll = (): void => {
        // scrollIntoViewIfNeeded ?
        messagesEndRef?.current?.scrollIntoView({ behavior: "smooth" });

        setNewMessagesBox(false);
    };

    const scrollToBottom = (always?: boolean): void => {
        if (!hideChat) {
            if (always) {
                setTimeout(() => triggerScroll(), 50);
                return;
            }
            if (messageWindowRef?.current?.scrollTop > -300) {
                setTimeout(() => triggerScroll(), 50);
                return;
            } else {
                const lastMessage = chatMessages[chatMessages?.length - 1];
                if (lastMessage?.userId !== currentUser?.id) {
                    setNewMessagesBox(true);
                }
            }
        }
    };

    useLayoutEffect(scrollToBottom, [chatMessages]);

    useEffect(() => {
        resetUnreadChatMessages();
        if (location.pathname === "/error") return null;
    }, []);

    if (isChatHidden) return null;

    return (
        <aside
            data-testid="chatbox-panel"
            className={cn(
                isMobile
                    ? "fixed flex h-[calc(100dvh-8.75rem)] w-full"
                    : `relative hidden max-h-full min-h-full min-w-[150px] max-w-[392px] xl:flex`,
                customHeight ? customHeight : "",
                fullSize ? "w-full rounded-b-lg md:max-w-full" : "w-[21.5vw] 2xl:w-[25.51vw]",
                "card card-border shrink-0 flex-col px-3 pt-1 pb-2 md:z-20 md:mb-0 md:rounded-tl-xl md:px-3 md:py-2 border-2 border-primary/30 shadow-xl"
            )}
        >
            {chatConfig?.CHAT_DISABLED ? (
                <div className="flex h-full flex-col items-center justify-center">
                    <div className="alert alert-warning max-w-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <div>
                            <h3 className="font-bold text-lg">Chat Disabled</h3>
                            <div className="text-sm">Please return later.</div>
                        </div>
                    </div>
                </div>
            ) : (
                <ErrorBoundary>
                    {hideHeader ? null : (
                        <ChatTopPanel
                            muteChat={muteChat}
                            setMuteChat={setMuteChat}
                            currentUser={currentUser}
                            socket={socket}
                            setHideChat={setHideChat}
                            chatRoom={isMainChat ? mainChatRoom : { room }}
                            fullSize={fullSize}
                        />
                    )}

                    <div
                        ref={messageWindowRef}
                        className="noscrollbar flex h-full flex-col-reverse overflow-y-auto overflow-x-hidden"
                    >
                        {!isLoading && !userIsLoading && isSocketConnected ? (
                            <div>
                                {chatMessages?.map((msg) => {
                                    return (
                                        <Fragment key={msg.id}>
                                            {msg.announcementType ? (
                                                <ConsoleMessage msg={msg} type={msg.announcementType} />
                                            ) : (
                                                <ChatMessage
                                                    msg={msg}
                                                    currentUser={currentUser}
                                                    setSelectedReplyMessage={setSelectedReplyMessage}
                                                    setFocusChatMsgInput={setFocusChatMsgInput}
                                                />
                                            )}
                                        </Fragment>
                                    );
                                })}
                                {newMessagesBox && (
                                    <div
                                        className="-translate-x-1/2 absolute bottom-24 left-1/2 z-20"
                                        onClick={() => scrollToBottom(true)}
                                    >
                                        <button className="btn btn-primary btn-sm shadow-lg animate-bounce">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                            </svg>
                                            New Messages
                                        </button>
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>
                        ) : (
                            <Spinner center />
                        )}
                    </div>
                    {chatConfig?.CHAT_MESSAGE_SENDING_DISABLED ? (
                        <div className="m-2">
                            <div className="alert alert-error">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <h3 className="font-bold">Messaging Disabled</h3>
                                    <div className="text-sm">Chat messages are temporarily disabled.</div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <ChatTextAreaInput
                            chatBannedUntil={currentUser?.chatBannedUntil}
                            socket={socket}
                            scrollToBottom={scrollToBottom}
                            chatRoom={room}
                            currentUser={currentUser}
                            selectedReplyMessage={selectedReplyMessage}
                            setSelectedReplyMessage={setSelectedReplyMessage}
                            focusChatMsgInput={focusChatMsgInput}
                            setFocusChatMsgInput={setFocusChatMsgInput}
                        />
                    )}
                </ErrorBoundary>
            )}
        </aside>
    );
};

export default Chatbox;
