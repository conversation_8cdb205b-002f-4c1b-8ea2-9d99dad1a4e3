import emojiImg from "@/assets/icons/UI/emojiButton.png";
import sendImg from "@/assets/icons/UI/sendButton.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import { api } from "@/helpers/api";
import useOuterClick from "@/hooks/useOuterClick";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { formatDistanceToNowStrict } from "date-fns";
import { AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "react-hot-toast";
import { countEmotesInMessage } from "../helpers/chatHelpers";
import EmotePicker from "./EmotePicker";
import RenderChatText from "./RenderChatText";
import { ChatMessage } from "../types/chat";
import { User } from "@/types/user";
import { Socket } from "socket.io-client";
import type { ChatRoom } from "../types/chat";

interface ChatTextAreaInputProps {
    chatBannedUntil: number;
    socket: Socket;
    scrollToBottom: (force?: boolean) => void;
    chatRoom: ChatRoom;
    currentUser: User;
    selectedReplyMessage?: ChatMessage | null;
    setSelectedReplyMessage: (message: ChatMessage | null) => void;
    focusChatMsgInput: boolean;
    setFocusChatMsgInput: (focus: boolean) => void;
}

export default function ChatTextAreaInput({
    chatBannedUntil,
    socket,
    scrollToBottom,
    chatRoom,
    currentUser,
    selectedReplyMessage = null,
    setSelectedReplyMessage,
    focusChatMsgInput,
    setFocusChatMsgInput,
}: ChatTextAreaInputProps) {
    const [userMessage, setUserMessage] = useState("");
    const [openEmoji, setOpenEmoji] = useState(false);
    const [messageBlocked, setMessageBlocked] = useState(0);
    const [noMessageError, setNoMessageError] = useState(false);
    const queryClient = useQueryClient();
    const chatMsgInputRef = useRef<HTMLTextAreaElement>(null);

    useEffect(() => {
        if (focusChatMsgInput) {
            setTimeout(() => {
                chatMsgInputRef.current?.focus();
                setFocusChatMsgInput(false);
            }, 100);
        }
    }, [focusChatMsgInput]);

    const innerRef = useOuterClick(() => {
        setOpenEmoji(false);
    });

    const handleSubmit = useCallback(() => {
        if (chatBannedUntil > 0) {
            queryClient.invalidateQueries({
                queryKey: api.user.getCurrentUserInfo.key(),
            });
            toast.error(`You are chatbanned for ${formatDistanceToNowStrict(new Date(chatBannedUntil))}!`);
            return;
        }

        if (messageBlocked >= 3) {
            toast.error("You are sending messages too fast!");
            return;
        }

        if (userMessage.length > 0) {
            if (countEmotesInMessage(userMessage) > 10) {
                toast.error("Too many emojis at once!");
                if (!noMessageError) {
                    setNoMessageError(true);
                    setTimeout(() => setNoMessageError(false), 1500);
                }
                return;
            }

            setMessageBlocked((current) => current + 1);
            setTimeout(() => {
                setMessageBlocked((current) => Math.max(0, current - 1));
            }, 5000);

            const submittedMessage = userMessage;

            const parentMessageId = selectedReplyMessage?.id || null;

            socket.emit("chat message", {
                message: submittedMessage,
                room: chatRoom,
                parentMessageId,
            });

            setSelectedReplyMessage(null);
            setUserMessage("");
            scrollToBottom(true);
        } else {
            setNoMessageError(true);
            toast.error("Message can't be blank!");
            setTimeout(() => setNoMessageError(false), 2000);
        }
    }, [
        chatBannedUntil,
        messageBlocked,
        userMessage,
        queryClient,
        socket,
        chatRoom,
        selectedReplyMessage,
        setSelectedReplyMessage,
        scrollToBottom,
        noMessageError,
    ]);

    const handleKeyDown = useCallback(
        (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
            if (event.key === "Enter") {
                event.preventDefault();
                handleSubmit();
            }
        },
        [handleSubmit]
    );

    const scrollToMessage = (messageId: number) => {
        const element = document.getElementById(`message-${messageId}`);
        if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    };

    const handleMessageChange = useCallback(
        (e: React.ChangeEvent<HTMLTextAreaElement>) => setUserMessage(e.target.value),
        []
    );

    return (
        <div className="my-3">
            <div
                className={cn(
                    "card relative flex w-full flex-col shadow-lg border-2",
                    noMessageError ? "border-error animate-pulse" : "border-primary/30",
                    "bg-gradient-to-br from-base-200 to-base-300"
                )}
            >
                {selectedReplyMessage && (
                    <div className="alert alert-info alert-sm mx-2 mt-2 p-2 border-l-4 border-l-accent">
                        <div className="flex items-center gap-2 w-full">
                            <div className="avatar">
                                <div className="mask mask-circle size-7">
                                    <DisplayAvatar src={selectedReplyMessage?.user} />
                                </div>
                            </div>
                            <div
                                className="flex-1 truncate cursor-pointer"
                                onClick={() => scrollToMessage(selectedReplyMessage.id)}
                            >
                                <RenderChatText
                                    msg={selectedReplyMessage}
                                    className="text-info-content text-sm truncate"
                                    imgClassName="max-h-5"
                                />
                            </div>
                            <button
                                className="btn btn-circle btn-ghost btn-xs"
                                onClick={() => setSelectedReplyMessage(null)}
                            >
                                <X className="size-4" />
                            </button>
                        </div>
                    </div>
                )}
                <div className="flex flex-row gap-2 p-2">
                    <textarea
                        ref={chatMsgInputRef}
                        required
                        id="chatMessage"
                        name="chatMessage"
                        maxLength={200}
                        rows={2}
                        cols={1}
                        placeholder="Type your message..."
                        value={userMessage}
                        className={cn(
                            "textarea textarea-primary flex-1 text-base-content placeholder:text-base-content/50",
                            "focus:outline-none focus:ring-2 focus:ring-accent"
                        )}
                        onChange={handleMessageChange}
                        onKeyDown={handleKeyDown}
                    />
                    <div
                        className="relative mx-1 my-auto"
                        onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            setOpenEmoji((prev) => !prev);
                        }}
                    >
                        <ButtonWrapper>
                            <img src={emojiImg} alt="" className="size-6 fill-white text-white" />
                        </ButtonWrapper>
                    </div>
                    <AnimatePresence>
                        {openEmoji && (
                            <EmotePicker
                                userMessage={userMessage}
                                setUserMessage={setUserMessage}
                                innerRef={innerRef}
                                currentUser={currentUser}
                            />
                        )}
                    </AnimatePresence>
                    <div className="relative mx-1 my-auto" onClick={handleSubmit}>
                        <ButtonWrapper>
                            <img src={sendImg} alt="" className="h-5 w-4 fill-white text-white" />
                        </ButtonWrapper>
                    </div>
                </div>
            </div>
        </div>
    );
}

interface ButtonWrapperProps {
    children: React.ReactNode;
}

const ButtonWrapper = React.memo(({ children }: ButtonWrapperProps) => (
    <button className="size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28287c] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110">
        <div className="flex items-center justify-center">{children}</div>
    </button>
));

ButtonWrapper.displayName = "ButtonWrapper";
