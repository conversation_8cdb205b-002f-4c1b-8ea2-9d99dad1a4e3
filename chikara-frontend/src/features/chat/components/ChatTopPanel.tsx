import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { ChevronRight, Scroll, Volume2, VolumeX, X } from "lucide-react";
import { Link } from "react-router-dom";
import { useSessionStore } from "../../../app/store/stores";
import ChatDropdownButton from "./ChatDropdownButton";
import { api } from "@/helpers/api";
import type { User } from "@/types/user";
import { Socket } from "socket.io-client";
import type { ChatRoom } from "../types/chat";

interface ChatTopPanelProps {
    muteChat: boolean;
    setMuteChat: (mute: boolean) => void;
    currentUser: User;
    socket: Socket;
    setHideChat: (hide: boolean) => void;
    chatRoom: ChatRoom;
    fullSize: boolean;
}

export default function ChatTopPanel({
    muteChat,
    setMuteChat,
    currentUser,
    socket,
    setHideChat,
    chatRoom,
    fullSize,
}: ChatTopPanelProps) {
    const { hidePollNotification, setHidePollNotification } = useSessionStore();
    const { data: availablePolls } = useQuery(api.suggestions.getAvailablePolls.queryOptions());

    return (
        <div className="navbar -mx-2 relative h-16 border-b-2 border-primary/20 bg-gradient-to-r from-base-200 to-base-300">
            <div className="navbar-start gap-2">
                <button 
                    className="btn btn-circle btn-sm btn-primary"
                    onClick={() => setMuteChat(!muteChat)}
                >
                    {muteChat ? (
                        <VolumeX size={18} />
                    ) : (
                        <Volume2 size={18} />
                    )}
                </button>
                
                <button className="btn btn-circle btn-sm btn-secondary">
                    <Scroll size={18} />
                </button>
            </div>

            <div className="navbar-center">
                <ChatDropdownButton chatRoom={chatRoom} currentUser={currentUser} />
            </div>

            <div className="navbar-end">
                <button
                    className="btn btn-circle btn-sm btn-accent hidden lg:flex"
                    onClick={() => setHideChat(true)}
                >
                    <ChevronRight size={18} />
                </button>
            </div>

            <div
                className={cn(
                    fullSize ? "md:-bottom-12 -bottom-10 md:h-12" : "-bottom-10",
                    "absolute left-0 z-2 h-11 w-[calc(100%-1px)]"
                )}
            >
                {!hidePollNotification && availablePolls?.length > 0 ? (
                    <div className="alert alert-info shadow-lg border-2 border-accent">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="h-6 w-6 shrink-0 stroke-current animate-bounce">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div className="flex-1">
                            <Link to="/polls" className="link link-accent font-bold text-lg">
                                Vote in the new Poll!
                            </Link>
                        </div>
                        <button
                            className="btn btn-circle btn-sm btn-ghost"
                            onClick={() => setHidePollNotification(true)}
                        >
                            <X className="size-4" />
                        </button>
                    </div>
                ) : null}
            </div>
        </div>
    );
}

