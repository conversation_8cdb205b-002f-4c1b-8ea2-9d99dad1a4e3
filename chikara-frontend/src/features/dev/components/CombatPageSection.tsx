import Button from "@/components/Buttons/Button";
import { api } from "@/helpers/api";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";

const CombatPageSection = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    // Mutation hooks for dev combat operations
    const fullHealMutation = useMutation(
        api.dev.fullHeal.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                toast.success("Full heal completed!");
            },
            onError: (error) => {
                console.error("Error performing full heal:", error);
                toast.error(error.message || "Failed to perform full heal");
            },
        })
    );

    const fullHealAllMutation = useMutation(
        api.dev.fullHealAll.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                toast.success("All users healed!");
            },
            onError: (error) => {
                console.error("Error healing all users:", error);
                toast.error(error.message || "Failed to heal all users");
            },
        })
    );

    const startPvpBattleMutation = useMutation(
        api.dev.startPvpBattle.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                queryClient.invalidateQueries({ queryKey: api.battle.getStatus.key() });
                navigate("/fight");
                toast.success("PvP battle started!");
            },
            onError: (error) => {
                console.error("Error initiating PvP battle:", error);
                toast.error(error.message || "Failed to start PvP battle");
            },
        })
    );

    const resetBattlesMutation = useMutation(
        api.dev.resetBattles.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                navigate("/home");
                toast.success("All battles reset!");
            },
            onError: (error) => {
                console.error("Error resetting battles:", error);
                toast.error(error.message || "Failed to reset battles");
            },
        })
    );

    const addRandomEffectsMutation = useMutation(
        api.dev.addRandomEffects.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getStatusEffects.key(),
                });
                toast.success("Random effects added!");
            },
            onError: (error) => {
                console.error("Error adding random effects:", error);
                toast.error(error.message || "Failed to add random effects");
            },
        })
    );

    const removeAllEffectsMutation = useMutation(
        api.dev.removeAllEffects.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getStatusEffects.key(),
                });
                toast.success("All effects removed!");
            },
            onError: (error) => {
                console.error("Error removing all effects:", error);
                toast.error(error.message || "Failed to remove all effects");
            },
        })
    );

    // Handler functions
    const fullHeal = () => {
        fullHealMutation.mutate({});
    };

    const healAllUsers = () => {
        fullHealAllMutation.mutate({});
    };

    const randomPVPBattle = () => {
        startPvpBattleMutation.mutate({});
    };

    const resetAllBattles = () => {
        resetBattlesMutation.mutate({});
    };

    const addRandomEffects = () => {
        addRandomEffectsMutation.mutate({});
    };

    const removeAllEffects = () => {
        removeAllEffectsMutation.mutate({});
    };

    return (
        <div className="grid grid-cols-2 gap-3 p-2">
            <Button className="text-sm!" variant="primary" onClick={randomPVPBattle}>
                Random PvP Battle
            </Button>
            <Button variant="primary" onClick={fullHeal}>
                Full Heal
            </Button>
            <Button className="text-sm!" variant="primary" onClick={healAllUsers}>
                Heal all Users
            </Button>
            <Button className="text-sm!" variant="primary" onClick={resetAllBattles}>
                Reset All Battles
            </Button>
            <Button className="text-sm!" variant="primary" onClick={addRandomEffects}>
                Random Status Effects
            </Button>
            <Button className="text-sm!" variant="primary" onClick={removeAllEffects}>
                Remove Status Effects
            </Button>
        </div>
    );
};

export default CombatPageSection;
