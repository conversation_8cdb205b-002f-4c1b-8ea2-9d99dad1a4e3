import { AlertCircle } from "lucide-react";

export const ExploreErrorState = () => {
    return (
        <div className="hero min-h-[400px]">
            <div className="hero-content text-center">
                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body items-center">
                        <div className="alert alert-error">
                            <AlertCircle className="w-6 h-6" />
                            <div>
                                <h3 className="font-bold">Failed to Load</h3>
                                <div className="text-sm">Unable to fetch exploration areas</div>
                            </div>
                        </div>
                        <button className="btn btn-primary btn-sm mt-4" onClick={() => window.location.reload()}>
                            Try Again
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export const ExploreLoadingState = () => {
    return (
        <div className="hero min-h-[400px]">
            <div className="hero-content text-center">
                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body items-center">
                        <span className="loading loading-spinner loading-lg text-primary"></span>
                        <h3 className="card-title mt-4">Loading District</h3>
                        <p className="text-base-content/70">
                            Discovering new exploration areas...
                        </p>
                        <progress className="progress progress-primary w-56 mt-4"></progress>
                    </div>
                </div>
            </div>
        </div>
    );
};
