import { useQueryClient } from "@tanstack/react-query";
import { <PERSON>, Grid3X3, Loader2, <PERSON>, Sparkles } from "lucide-react";
import { useCallback, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { NodeMapContainer } from "@/components/NodeMap";
import useExploreInteract from "@/features/explore/api/useExploreInteract";
import { api } from "@/helpers/api";
import { formatTimeUntilExpiration, isExpiringSoon } from "@/helpers/dateHelpers";
import { cn } from "@/lib/utils";
import { useNormalStore } from "../../../app/store/stores";
import { mapContainerVariants, nodeButtonVariants, cardBackgroundVariants } from "../styles/explore.styles";
import {
    getNodeTypeBadge,
    getNodeTypeColor,
    getNodeTypeIcon,
    getStatusBadge,
    getNodeTypeBackgroundHint,
    getNodeTypeVisualInfo,
} from "../styles/node.styles";
import type { ExploreNodeLocation, MapNodeData, ExploreCharacterDialogue } from "../types/explore.types";
import { sortNodesByStatus } from "../utils/explore.utils";
import { CharacterEncounterView } from "./CharacterEncounterView";
import { CommonLocations } from "./CommonLocations";
import { ExploreErrorState, ExploreLoadingState } from "./ExploreFetchState";
import { ForagingView } from "./ExploreForagingView";
import { MiningView } from "./ExploreMiningView";
import { ScavengeView } from "./ExploreScavengeView";
import { MapBackground } from "./MapBackground";
import type { StoryEpisodeData } from "@/features/story/types/story";
import { StoryEpisodePlayer } from "@/features/story/components/StoryEpisodePlayer";

interface ExploreViewProps {
    className?: string;
    viewType: "list" | "map";
    currentView?: ExploreNodeLocation | null;
    mapData: MapNodeData[] | undefined;
    isLoading: boolean;
    error: Error | null;
}

export const ExploreView = ({ mapData, isLoading, error, className, viewType, currentView }: ExploreViewProps) => {
    const mapContainerRef = useRef<HTMLDivElement>(null);

    const [selectedNodeId, setSelectedNodeId] = useState<number | null>(null);
    const [showGridOverlay, setShowGridOverlay] = useState(false);

    const { mutate: interactWithNode, isPending: isInteracting } = useExploreInteract({
        onSelectedNodeChange: setSelectedNodeId,
    });

    const { setJustJailed } = useNormalStore();
    const navigate = useNavigate();
    const queryClient = useQueryClient();

    const handleNodeClick = useCallback((nodeId: number) => {
        setSelectedNodeId(nodeId);
    }, []);

    // Find current node from map data
    const currentNode = mapData?.find((node) => node.status === "current");

    // Sort nodes by status (available first, then completed, then locked)
    const sortedNodes = useMemo(() => {
        if (!mapData) return [];
        return sortNodesByStatus(mapData);
    }, [mapData]);

    const handleNodeInteract = useCallback(
        (nodeId: number, shopId?: number | null) => {
            // Prevent interaction during travel

            const node = mapData?.find((n) => n.id === nodeId);
            if (!node || node.status !== "available") return;

            setSelectedNodeId(nodeId);
            const isStatic = node.isStatic;

            if (node.nodeType === "SHOP") {
                navigate(`/shops/${shopId || node.shopId || nodeId}`);
                return;
            }

            interactWithNode({ nodeId, isStatic });
        },
        [mapData, interactWithNode, navigate]
    );

    const handleCloseEncounter = async () => {
        queryClient.invalidateQueries({
            queryKey: api.user.getCurrentUserInfo.key(),
        });
        setJustJailed(false);
        await queryClient.invalidateQueries({
            queryKey: api.explore.getMapByLocation.key(),
        });
    };

    const handleCloseStoryEpisode = async () => {
        await queryClient.invalidateQueries({
            queryKey: api.explore.getMapByLocation.key(),
        });
    };

    // Check if there's a current character encounter node with encounter data
    if (currentNode && currentNode.nodeType === "CHARACTER_ENCOUNTER" && currentNode.metadata?.dialogue) {
        return (
            <CharacterEncounterView
                dialogue={currentNode.metadata.dialogue as ExploreCharacterDialogue}
                healed={currentNode.metadata.healed as boolean | undefined}
                nodeId={currentNode.id}
                onClose={handleCloseEncounter}
            />
        );
    }

    if (currentNode && currentNode.nodeType === "STORY" && currentNode.metadata?.episodeData) {
        return (
            <StoryEpisodePlayer
                episodeData={currentNode.metadata.episodeData as StoryEpisodeData}
                onClose={handleCloseStoryEpisode}
            />
        );
    }

    // Check if there's a current scavenging node
    if (currentNode && currentNode.nodeType === "SCAVENGE_NODE" && currentNode.metadata?.choices) {
        return (
            <ScavengeView
                nodeId={currentNode.id}
                location={currentNode.location}
                choices={currentNode.metadata.choices as string[]}
                onClose={handleCloseEncounter}
            />
        );
    }

    // Check if there's a current mining node
    if (currentNode && currentNode.nodeType === "MINING_NODE" && currentNode.status === "current") {
        const miningType = (currentNode.metadata?.miningType as string) || "ore";
        const difficulty = (currentNode.metadata?.difficulty as "easy" | "medium" | "hard") || "easy";

        return (
            <MiningView
                nodeId={currentNode.id}
                location={currentNode.location}
                miningType={miningType}
                difficulty={difficulty}
                onClose={handleCloseEncounter}
            />
        );
    }

    // Check if there's a current foraging node
    if (currentNode && currentNode.nodeType === "FORAGING_NODE" && currentNode.status === "current") {
        const foragingType = (currentNode.metadata?.foragingType as string) || "herbs";
        const difficulty = (currentNode.metadata?.difficulty as "easy" | "medium" | "hard") || "easy";

        return (
            <ForagingView
                nodeId={currentNode.id}
                location={currentNode.location}
                foragingType={foragingType}
                difficulty={difficulty}
                onClose={handleCloseEncounter}
            />
        );
    }

    if (isLoading) {
        return <ExploreLoadingState />;
    }

    if (error) {
        return <ExploreErrorState />;
    }

    if (viewType === "map") {
        return (
            <div className={`relative ${className || ""}`}>
                {isInteracting && (
                    <div className="absolute inset-0 bg-base-300/50 backdrop-blur-sm z-50 flex items-center justify-center rounded-box">
                        <div className="card bg-base-100">
                            <div className="card-body p-6 flex-row items-center gap-3">
                                <span className="loading loading-spinner loading-md text-primary"></span>
                                <span className="text-base-content font-semibold">Interacting...</span>
                            </div>
                        </div>
                    </div>
                )}

                {/* Desktop Layout: Sidebar + Map */}
                <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
                    {/* Map Container */}
                    <div ref={mapContainerRef} className="flex-1 min-w-0 relative min-h-[400px] lg:min-h-[600px]">
                        {/* Map background */}
                        <div className="absolute inset-0 overflow-hidden rounded-lg">
                            <MapBackground currentView={currentView || "shibuya"} className="w-full h-full" />
                        </div>

                        {/* Development Grid Overlay Toggle */}
                        {import.meta.env.MODE === "development" && (
                            <div className="absolute top-4 left-4 z-20">
                                <button
                                    title="Toggle 5x5 Grid Overlay"
                                    className={cn(
                                        "btn btn-sm",
                                        showGridOverlay ? "btn-primary" : "btn-ghost"
                                    )}
                                    onClick={() => setShowGridOverlay(!showGridOverlay)}
                                >
                                    <Grid3X3 className="w-4 h-4" />
                                    <span className="hidden sm:inline">Grid</span>
                                </button>
                            </div>
                        )}

                        {/* Node overlay */}
                        <div className="relative z-10 w-full h-full">
                            <NodeMapContainer
                                showTooltips
                                showAnimations
                                showGridOverlay={showGridOverlay}
                                nodes={mapData || []}
                                connections={[]}
                                mapType="isolated" // Using isolated type for explore nodes
                                interactive={false} // Disable map controls (pan, zoom, etc.)
                                className={cn(
                                    mapContainerVariants({ background: "default" }),
                                    "bg-transparent border-0 w-full h-full" // Make container transparent to show map
                                )}
                                onNodeClick={handleNodeClick}
                                onAccessNode={handleNodeInteract}
                            />
                        </div>
                    </div>
                    <div className="lg:w-80 lg:flex-shrink-0">
                        <CommonLocations />
                    </div>
                </div>
            </div>
        );
    }

    // Enhanced List view
    return (
        <div className={cn("grid grid-cols-1 lg:grid-cols-2 gap-8", className)}>
            {/* Enhanced List View */}
            <div className="space-y-3">
                <div className="mb-6">
                    <div className="flex items-center gap-3 mb-2">
                        <div className="badge badge-primary badge-lg">
                            <Sparkles className="w-4 h-4 mr-1" />
                            District Locations
                        </div>
                    </div>
                    <p className="text-sm text-base-content/70">Explore what this district has to offer</p>
                </div>

                <div className="space-y-3">
                    {sortedNodes.map((node, index) => {
                        const statusBadge = getStatusBadge(node.status);
                        const typeBadge = getNodeTypeBadge(node.nodeType);
                        const visualInfo = getNodeTypeVisualInfo(node.nodeType);
                        const isAvailable = node.status === "available";
                        const isLocked = node.status === "locked";
                        const isInteractable = isAvailable && !isInteracting && selectedNodeId !== node.id;

                        return (
                            <div
                                key={node.id}
                                className="relative"
                                style={{ animationDelay: `${index * 50}ms` }}
                            >
                                <button
                                    disabled={!isAvailable || isInteracting || selectedNodeId === node.id}
                                    className={cn(
                                        "card w-full",
                                        isAvailable ? "card-border hover:shadow-xl" : "",
                                        !isAvailable && "opacity-60",
                                        "transition-all duration-300",
                                        isInteractable && "hover:scale-[1.02]",
                                        node.nodeType === "STORY" && isAvailable && "ring-2 ring-warning ring-offset-2 ring-offset-base-100"
                                    )}
                                    onClick={() => isAvailable && handleNodeInteract(node.id, node.shopId || null)}
                                >
                                    {/* Special glow effects */}
                                    {isAvailable && node.nodeType === "STORY" && (
                                        <div className="absolute inset-0 bg-gradient-to-r from-warning/20 to-accent/20 rounded-box animate-pulse pointer-events-none" />
                                    )}

                                    <div className="card-body p-4">
                                        <div className="flex items-start gap-4">
                                            {/* Icon with indicator */}
                                            <div className="indicator">
                                                <div className={cn(
                                                    "avatar placeholder",
                                                    isLocked && "opacity-50"
                                                )}>
                                                    <div className={cn(
                                                        "w-12 h-12 rounded-xl",
                                                        node.nodeType === "BATTLE" && "bg-gradient-to-br from-error to-error/60",
                                                        node.nodeType === "SHOP" && "bg-gradient-to-br from-success to-success/60",
                                                        node.nodeType === "CHARACTER_ENCOUNTER" && "bg-gradient-to-br from-info to-info/60",
                                                        node.nodeType === "STORY" && "bg-gradient-to-br from-warning to-warning/60",
                                                        node.nodeType === "SCAVENGE_NODE" && "bg-gradient-to-br from-secondary to-secondary/60",
                                                        node.nodeType === "MINING_NODE" && "bg-gradient-to-br from-accent to-accent/60",
                                                        node.nodeType === "FORAGING_NODE" && "bg-gradient-to-br from-primary to-primary/60",
                                                        !node.nodeType && "bg-gradient-to-br from-base-300 to-base-200"
                                                    )}>
                                                        <span className="text-white">{getNodeTypeIcon(node.nodeType)}</span>
                                                    </div>
                                                </div>
                                                {isLocked && (
                                                    <span className="indicator-item badge badge-neutral badge-sm">
                                                        <Lock className="w-3 h-3" />
                                                    </span>
                                                )}
                                                {isAvailable && node.nodeType === "STORY" && (
                                                    <span className="indicator-item badge badge-warning badge-sm animate-pulse">
                                                        <Sparkles className="w-3 h-3" />
                                                    </span>
                                                )}
                                                {isAvailable && node.nodeType !== "STORY" && (
                                                    <span className="indicator-item badge badge-success badge-sm animate-pulse"></span>
                                                )}
                                            </div>

                                            {/* Content */}
                                            <div className="flex-1">
                                                <h2 className="card-title text-base">
                                                    {node.title}
                                                    {node.nodeType === "STORY" && (
                                                        <Sparkles className="w-4 h-4 text-warning animate-pulse" />
                                                    )}
                                                </h2>
                                                
                                                <div className="flex flex-wrap gap-2 mt-2">
                                                    <span className={cn(
                                                        "badge badge-sm font-bold",
                                                        node.nodeType === "BATTLE" && "badge-error",
                                                        node.nodeType === "SHOP" && "badge-success",
                                                        node.nodeType === "CHARACTER_ENCOUNTER" && "badge-info",
                                                        node.nodeType === "STORY" && "badge-warning",
                                                        node.nodeType === "SCAVENGE_NODE" && "badge-secondary",
                                                        node.nodeType === "MINING_NODE" && "badge-accent",
                                                        node.nodeType === "FORAGING_NODE" && "badge-primary",
                                                        !node.nodeType && "badge-ghost"
                                                    )}>
                                                        {typeBadge.text}
                                                    </span>
                                                    {!node.isStatic && node.status !== "available" && node.status !== "current" && (
                                                        <span className={cn(
                                                            "badge badge-sm badge-outline",
                                                            node.status === "completed" && "badge-neutral",
                                                            node.status === "locked" && "badge-ghost"
                                                        )}>
                                                            {statusBadge.text}
                                                        </span>
                                                    )}
                                                </div>

                                                <p className="text-sm text-base-content/70 mt-3 line-clamp-2">
                                                    {node.description}
                                                </p>
                                                <p className="text-xs text-base-content/50 italic mt-1">
                                                    {node.nodeType === "STORY"
                                                        ? "Story episode - advances main quest progress"
                                                        : visualInfo.description}
                                                </p>

                                                {/* Expiration info */}
                                                {node.expiresAt && !node.isStatic && (
                                                    <div className="mt-3">
                                                        <div className={cn(
                                                            "badge gap-1",
                                                            isExpiringSoon(node.expiresAt)
                                                                ? "badge-warning badge-sm"
                                                                : "badge-info badge-sm"
                                                        )}>
                                                            <Clock className="w-3 h-3" />
                                                            {formatTimeUntilExpiration(node.expiresAt)}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Common Locations Section for List View */}
            <CommonLocations />
        </div>
    );
};
