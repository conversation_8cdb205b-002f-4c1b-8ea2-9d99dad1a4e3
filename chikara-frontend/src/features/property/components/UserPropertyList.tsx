import { useGetUserProperties } from "../api/useGetUserProperties";
import { useSellProperty, useSetPrimaryProperty } from "../api/usePropertyMutations";
import { UserProperty } from "../types/property";
import { Home, DollarSign, Star, Calendar, Settings, Crown, Sparkles, ChevronRight, Shield } from "lucide-react";

interface UserPropertyCardProps {
    userProperty: UserProperty;
    onSell: (_propertyId: number) => void;
    onSetPrimary: (_propertyId: number) => void;
    isSellingProperty: boolean;
    isSettingPrimary: boolean;
}

const UserPropertyCard = ({
    userProperty,
    onSell,
    onSetPrimary,
    isSellingProperty,
    isSettingPrimary,
}: UserPropertyCardProps) => {
    const { property } = userProperty;
    const sellPrice = Math.floor(property.cost * 0.2); // 20% of original cost

    return (
        <div className={`card ${userProperty.isPrimary ? 'bg-gradient-to-br from-warning/20 to-warning/5 border-2 border-warning shadow-2xl' : 'bg-base-300 border-2 border-base-content/10'} hover:shadow-xl transition-all duration-300`}>
            <div className="card-body">
                <div className="flex items-start gap-4">
                    <div className="indicator">
                        {userProperty.isPrimary && (
                            <span className="indicator-item badge badge-warning badge-sm">
                                <Crown className="w-3 h-3" />
                            </span>
                        )}
                        <div className="avatar placeholder">
                            <div className={`rounded-xl w-16 h-16 ${userProperty.isPrimary ? 'bg-gradient-to-br from-warning to-secondary' : 'bg-gradient-to-br from-accent to-info'}`}>
                                <Home className="w-8 h-8 text-white" />
                            </div>
                        </div>
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                            <h3 className="card-title text-2xl">
                                {property.name}
                            </h3>
                            {userProperty.isPrimary && (
                                <div className="badge badge-warning gap-1">
                                    <Star className="w-3 h-3" />
                                    Primary
                                </div>
                            )}
                        </div>
                        <p className="text-base-content/70 mb-4">{property.description}</p>

                        <div className="stats stats-horizontal bg-base-100 shadow mb-4">
                            <div className="stat py-2">
                                <div className="stat-figure text-primary">
                                    <Calendar className="w-5 h-5" />
                                </div>
                                <div className="stat-title text-xs">Purchased</div>
                                <div className="stat-value text-sm">
                                    {new Date(userProperty.purchaseDate).toLocaleDateString()}
                                </div>
                            </div>
                            <div className="stat py-2">
                                <div className="stat-figure text-success">
                                    <DollarSign className="w-5 h-5" />
                                </div>
                                <div className="stat-title text-xs">Value</div>
                                <div className="stat-value text-sm text-success">
                                    ${property.cost.toLocaleString()}
                                </div>
                            </div>
                            <div className="stat py-2">
                                <div className="stat-figure text-info">
                                    <Shield className="w-5 h-5" />
                                </div>
                                <div className="stat-title text-xs">Slots</div>
                                <div className="stat-value text-sm text-info">{property.slots}</div>
                            </div>
                        </div>

                        {property.buffs && Object.keys(property.buffs).length > 0 && (
                            <div className="alert alert-info mb-4">
                                <Sparkles className="w-5 h-5" />
                                <div>
                                    <h4 className="font-bold mb-1">Active Buffs</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {Object.entries(property.buffs).map(([buff, value]) => (
                                            <div key={buff} className="badge badge-primary badge-outline">
                                                {buff}: {typeof value === "number"
                                                    ? `+${(((value as number) - 1) * 100).toFixed(0)}%`
                                                    : String(value)}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="card-actions justify-end">
                            {!userProperty.isPrimary && (
                                <button
                                    className="btn btn-warning btn-sm"
                                    disabled={isSettingPrimary}
                                    onClick={() => onSetPrimary(property.id)}
                                >
                                    {isSettingPrimary ? (
                                        <><span className="loading loading-spinner loading-xs"></span> Setting...</>
                                    ) : (
                                        <><Star className="w-4 h-4" /> Set Primary</>
                                    )}
                                </button>
                            )}
                            {!userProperty.isPrimary && (
                                <button
                                    className="btn btn-error btn-sm"
                                    disabled={isSellingProperty}
                                    onClick={() => onSell(property.id)}
                                >
                                    {isSellingProperty ? (
                                        <><span className="loading loading-spinner loading-xs"></span> Selling...</>
                                    ) : (
                                        <><DollarSign className="w-4 h-4" /> Sell for ${sellPrice.toLocaleString()}</>
                                    )}
                                </button>
                            )}
                            <button className="btn btn-ghost btn-sm" disabled>
                                <Settings className="w-4 h-4" />
                                Customize
                                <div className="badge badge-sm">Soon</div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export const UserPropertyList = () => {
    const { data: userProperties, isLoading, error } = useGetUserProperties();
    const sellProperty = useSellProperty();
    const setPrimaryProperty = useSetPrimaryProperty();

    const handleSell = (propertyId: number) => {
        sellProperty.mutate({ propertyId });
    };

    const handleSetPrimary = (propertyId: number) => {
        setPrimaryProperty.mutate({ propertyId });
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center py-16">
                <span className="loading loading-dots loading-lg text-secondary"></span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="alert alert-error shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Failed to load your properties. Please try again later.</span>
            </div>
        );
    }

    if (!userProperties || userProperties.length === 0) {
        return (
            <div className="hero bg-base-300 rounded-lg py-12">
                <div className="hero-content text-center">
                    <div className="max-w-md">
                        <Crown className="w-16 h-16 mx-auto mb-4 text-warning" />
                        <h3 className="text-2xl font-bold mb-2">No Properties Yet</h3>
                        <p className="text-base-content/70">
                            Start building your empire! Purchase your first property from the marketplace below.
                        </p>
                        <div className="mt-4">
                            <ChevronRight className="w-8 h-8 mx-auto animate-bounce text-primary" />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-3xl font-bold flex items-center gap-3">
                    <div className="badge badge-secondary badge-lg">
                        <Crown className="w-4 h-4 mr-1" />
                        My Empire
                    </div>
                    Your Properties
                </h2>
                <div className="stats bg-base-300 shadow">
                    <div className="stat py-2 px-4">
                        <div className="stat-title text-xs">Total Properties</div>
                        <div className="stat-value text-lg">{userProperties.length}</div>
                    </div>
                </div>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-1">
                {userProperties.map((userProperty) => (
                    <UserPropertyCard
                        key={userProperty.id}
                        userProperty={userProperty}
                        isSellingProperty={sellProperty.isPending}
                        isSettingPrimary={setPrimaryProperty.isPending}
                        onSell={handleSell}
                        onSetPrimary={handleSetPrimary}
                    />
                ))}
            </div>
        </div>
    );
};
