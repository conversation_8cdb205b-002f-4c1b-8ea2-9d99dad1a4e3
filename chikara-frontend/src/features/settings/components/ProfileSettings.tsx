import uploadImageIcon from "@/assets/icons/uploadimageicon.svg";
import defaultAvatar from "@/assets/images/defaultAvatar.png";
import { User } from "@/types/user";
import { Camera, ImagePlus, Save, User as UserIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import useUpdateProfile from "../api/useUpdateProfile";
import ImageCropper from "./ImageCropper";

interface ProfileSettingsProps {
    currentUser?: User;
}

interface SelectedFile {
    blob: Blob;
    name: string;
}

export default function ProfileSettings({ currentUser }: ProfileSettingsProps) {
    const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);
    const [avatarPreview, setAvatarPreview] = useState<string | undefined>();
    const [selectedBanner, setSelectedBanner] = useState<SelectedFile | null>(null);
    const [selectedCrop, setSelectedCrop] = useState<File | null>(null);
    const [bannerPreview, setBannerPreview] = useState<string | null>(null);
    const [openCropper, setOpenCropper] = useState<boolean>(false);
    const [username, setUsername] = useState<string>(currentUser?.username || "");
    const [description, setDescription] = useState<string>(currentUser?.about || "");
    const [gender, setGender] = useState<string>("male");
    const updateProfileMutation = useUpdateProfile();

    // TODO - Implement this
    const isProfileBannerQuestComplete = true;

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
        e.preventDefault();

        if (username.length > 17) {
            toast.error("Student name is too long!");
            return;
        }
        if (username.length < 4) {
            toast.error("Student name is too short!");
            return;
        }
        if (description.length > 500) {
            toast.error("Description is too long!");
            return;
        }

        // Create the input object for ORPC
        const updateData: {
            username: string;
            about: string;
            avatar?: File;
            banner?: Blob;
        } = {
            username,
            about: description,
        };

        if (selectedAvatar) {
            if (currentUser?.userType === "admin" && import.meta.env.MODE !== "development") {
                toast.error("Don't change admin avatars");
                return;
            }
            updateData.avatar = selectedAvatar;
        }

        if (selectedBanner) {
            updateData.banner = selectedBanner.blob;
        }

        updateProfileMutation.mutate(updateData);
    };

    useEffect(() => {
        if (!selectedAvatar) {
            if (currentUser?.avatar) {
                setAvatarPreview(`/${currentUser?.avatar}`);
            } else {
                setAvatarPreview(defaultAvatar);
            }

            return;
        }
        const objectUrl = URL.createObjectURL(selectedAvatar);
        setAvatarPreview(objectUrl);

        // free memory when ever this component is unmounted
        return () => URL.revokeObjectURL(objectUrl);
    }, [selectedAvatar]);

    const uploadBanner = (e: React.ChangeEvent<HTMLInputElement>): void => {
        e.preventDefault();
        if (e.target.files && e.target.files[0]) {
            setSelectedCrop(e.target.files[0]);
            setOpenCropper(true);
        }
    };

    useEffect(() => {
        if (!selectedBanner) {
            if (currentUser?.profileBanner) {
                setBannerPreview(`/${currentUser?.profileBanner}`);
            } else {
                setBannerPreview(uploadImageIcon);
            }

            return;
        }
        // const objectUrl = URL.createObjectURL(selectedBanner);
        // setBannerPreview(objectUrl);

        // // free memory when ever this component is unmounted
        // return () => URL.revokeObjectURL(objectUrl);
    }, [selectedBanner]);

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <div className="card bg-base-200">
                <div className="card-body">
                    <h2 className="card-title text-2xl text-primary mb-4">
                        <UserIcon className="size-6" />
                        Profile Settings
                    </h2>
                    <div className="alert alert-warning alert-soft mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <span className="text-sm">Your profile information is visible to other players</span>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Form Fields */}
                        <div className="lg:col-span-2 space-y-6">
                            <div className="form-control">
                                <label className="label" htmlFor="username">
                                    <span className="label-text font-semibold">Student Name</span>
                                    <span className="label-text-alt text-warning">{username.length}/17</span>
                                </label>
                                <input
                                    value={username}
                                    type="text"
                                    name="username"
                                    id="username"
                                    autoComplete="username"
                                    className="input input-primary"
                                    placeholder="Enter your student name"
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        setUsername(e.target.value);
                                    }}
                                />
                                <label className="label">
                                    <span className="label-text-alt">Must be 4-17 characters</span>
                                </label>
                            </div>

                            <div className="form-control">
                                <label className="label" htmlFor="about">
                                    <span className="label-text font-semibold">Profile Description</span>
                                    <span className="label-text-alt text-warning">{description.length}/500</span>
                                </label>
                                <textarea
                                    id="about"
                                    name="about"
                                    rows={4}
                                    maxLength={500}
                                    className="textarea textarea-primary"
                                    placeholder="Tell other students about yourself..."
                                    value={description}
                                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                                        setDescription(e.target.value);
                                    }}
                                />
                                <label className="label">
                                    <span className="label-text-alt">Brief description visible on your profile</span>
                                </label>
                            </div>

                            <div className="form-control">
                                <label className="label" htmlFor="gender">
                                    <span className="label-text font-semibold">Gender</span>
                                </label>
                                <select
                                    id="gender"
                                    name="gender"
                                    className="select select-primary"
                                    value={gender}
                                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setGender(e.target.value)}
                                >
                                    <option value="male">--</option>
                                    {/* <option value="female">Female</option> */}
                                </select>
                            </div>
                        </div>

                        {/* Avatar Section */}
                        <div className="space-y-6">
                            <div className="card bg-base-300 border-2 border-primary/20">
                                <div className="card-body items-center text-center">
                                    <h3 className="text-lg font-semibold text-primary mb-4">Avatar</h3>
                                    
                                    {/* Mobile Avatar */}
                                    <div className="lg:hidden">
                                        <div className="avatar">
                                            <div className="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                                <img src={avatarPreview} alt="Avatar" />
                                            </div>
                                        </div>
                                        <label htmlFor="user-photo-mobile" className="btn btn-primary btn-sm mt-4 gap-2">
                                            <Camera className="size-4" />
                                            Change Avatar
                                        </label>
                                        <input
                                            id="user-photo-mobile"
                                            type="file"
                                            accept="image/*"
                                            className="hidden"
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                if (e.target.files && e.target.files[0]) {
                                                    setSelectedAvatar(e.target.files[0]);
                                                }
                                            }}
                                        />
                                    </div>
                                    
                                    {/* Desktop Avatar */}
                                    <div className="hidden lg:block">
                                        <div className="relative group">
                                            <div className="avatar">
                                                <div className="w-40 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                                    <img src={avatarPreview} alt="Avatar" />
                                                </div>
                                            </div>
                                            <label
                                                htmlFor="user-photo"
                                                className="absolute inset-0 flex items-center justify-center bg-black/75 rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                                            >
                                                <div className="text-white text-center">
                                                    <Camera className="size-8 mx-auto mb-1" />
                                                    <span className="text-sm font-medium">Change</span>
                                                </div>
                                                <input
                                                    type="file"
                                                    id="user-photo"
                                                    accept="image/*"
                                                    className="hidden"
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                        if (e.target.files && e.target.files[0]) {
                                                            setSelectedAvatar(e.target.files[0]);
                                                        }
                                                    }}
                                                />
                                            </label>
                                        </div>
                                    </div>
                                    
                                    {selectedAvatar && (
                                        <div className="badge badge-success badge-sm mt-2 gap-1">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-4 h-4 stroke-current">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            New avatar selected
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Profile Banner Section */}
                    <div className="mt-8">
                        <div className="card bg-base-300 border-2 border-accent/20">
                            <div className="card-body">
                                <h3 className="text-lg font-semibold text-accent mb-4 flex items-center gap-2">
                                    <ImagePlus className="size-5" />
                                    Profile Banner
                                    {!isProfileBannerQuestComplete && (
                                        <span className="badge badge-warning badge-sm">Quest Required</span>
                                    )}
                                </h3>
                                
                                <div className="relative">
                                    <label
                                        htmlFor="profile-banner"
                                        className={
                                            selectedBanner || currentUser?.profileBanner
                                                ? "block cursor-pointer group"
                                                : "flex flex-col items-center justify-center h-40 border-2 border-dashed border-accent/40 rounded-lg cursor-pointer hover:bg-accent/5 transition-colors group"
                                        }
                                    >
                                        {selectedBanner || currentUser?.profileBanner ? (
                                            <div className="relative overflow-hidden rounded-lg">
                                                <img
                                                    src={bannerPreview}
                                                    alt="Profile banner"
                                                    className="w-full h-40 object-cover"
                                                />
                                                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                                    <div className="text-white text-center">
                                                        <ImagePlus className="size-8 mx-auto mb-1" />
                                                        <span className="text-sm font-medium">Change Banner</span>
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <>
                                                <ImagePlus className="size-12 text-accent/40 group-hover:text-accent/60 transition-colors" />
                                                <span className="mt-2 text-sm text-base-content/60 group-hover:text-base-content/80 transition-colors">
                                                    Click to upload banner image
                                                </span>
                                            </>
                                        )}

                                        <input
                                            disabled={!isProfileBannerQuestComplete}
                                            type="file"
                                            accept="image/*"
                                            id="profile-banner"
                                            className="hidden"
                                            onChange={uploadBanner}
                                        />
                                    </label>
                                </div>
                                
                                {selectedBanner?.name && (
                                    <div className="badge badge-info badge-sm mt-2 gap-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="inline-block w-4 h-4 stroke-current">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        {selectedBanner.name}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <ImageCropper
                        src={selectedCrop}
                        setPreview={setBannerPreview}
                        setSelected={setSelectedBanner}
                        open={openCropper}
                        setOpen={setOpenCropper}
                        setSrc={setSelectedCrop}
                    />
                </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
                <button
                    type="submit"
                    className="btn btn-primary btn-wide gap-2"
                    disabled={updateProfileMutation.isPending}
                >
                    {updateProfileMutation.isPending ? (
                        <>
                            <span className="loading loading-spinner loading-sm"></span>
                            Saving...
                        </>
                    ) : (
                        <>
                            <Save className="size-5" />
                            Save Changes
                        </>
                    )}
                </button>
            </div>
        </form>
    );
}
