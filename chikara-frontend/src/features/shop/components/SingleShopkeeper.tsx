import yenImg from "@/assets/icons/UI/yen.png";
import TraderRep from "@/components/TraderRep";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { cn } from "@/lib/utils";
import { ArrowLeft, ShoppingBag, Package, Store, ClipboardList, Coins } from "lucide-react";
import { Link } from "react-router-dom";
import type { Shop } from "../types/shop";

export type SellOrBuyTab = "Buy" | "Sell";

interface SingleShopkeeperProps {
    singleShop: Shop;
    setSellOrBuyTab: (_tab: SellOrBuyTab) => void;
    sellOrBuyTab: SellOrBuyTab;
    cash: string;
}

const shopkeeperTranslateY = (name: string) => {
    switch (name) {
        case "Nagao":
            return "translate-y-28 -translate-x-2";
        case "Goda":
            return "translate-y-36";
        case "Mihara":
            return "translate-y-32 translate-x-4";
        case "Shoko":
            return "translate-y-36 translate-x-4";
        case "Otake":
            return "translate-y-36 translate-x-2";
        case "Honda":
            return "translate-y-36 translate-x-4";
        default:
            return "translate-y-36";
    }
};

const shopkeeperImageSmall = (name: string) => {
    const basePath = import.meta.env.VITE_IMAGE_CDN_URL + `/static/characters`;
    const capitalisedName = capitaliseFirstLetter(name);
    switch (capitalisedName) {
        case "Nagao":
            return basePath + "/Nagao/happyopenSmall.webp";
        case "Goda":
            return basePath + "/Goda/happyopenSmall.webp";
        case "Mihara":
            return basePath + "/Mihara/happySmall.webp";
        case "Shoko":
            return basePath + "/Shoko/happyopenSmall.webp";
        case "Otake":
            return basePath + "/Otake/neutralSmall.webp";
        case "Honda":
            return basePath + "/Honda/neutralSmall.webp";
        default:
            return "";
    }
};

function SingleShopkeeper({ singleShop, setSellOrBuyTab, sellOrBuyTab, cash }: SingleShopkeeperProps) {
    const currentTab = (tabname: SellOrBuyTab) => {
        if (sellOrBuyTab === tabname) {
            return true;
        } else {
            return false;
        }
    };

    const buyOrSellTabs = [
        { name: "Buy" as const, current: currentTab("Buy") },
        { name: "Sell" as const, current: currentTab("Sell") },
    ];

    return (
        <div className="rounded-xl bg-[#162639] border border-[#2a4a7c] shadow-xl h-fit">
            {/* Back Button - Desktop */}
            <Link to={-1} className="hidden md:block">
                <button className="absolute -top-10 left-0 px-4 py-1.5 text-sm text-gray-300 hover:text-white transition-colors duration-200 flex items-center gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    Back
                </button>
            </Link>
            
            {/* Buy/Sell Tabs - Desktop */}
            <div className="hidden sm:block">
                <div className="flex rounded-t-xl overflow-hidden">
                    {buyOrSellTabs.map((tab) => (
                        <button
                            key={tab.name}
                            data-testid={tab.name === "Buy" ? "buy-item-button" : "sell-item-button"}
                            className={cn(
                                "flex-1 px-4 py-3 text-center font-medium transition-all duration-200 border-b-2",
                                tab.current
                                    ? "text-white bg-[#1a2f4a] border-blue-500"
                                    : "text-gray-400 bg-[#162639] border-transparent hover:text-gray-300 hover:bg-[#1a2f4a]/50"
                            )}
                            onClick={() => setSellOrBuyTab(tab.name)}
                        >
                            <div className="flex items-center justify-center">
                                {tab.name === "Buy" ? (
                                    <ShoppingBag className="mr-2 h-5 w-5" />
                                ) : (
                                    <Package className="mr-2 h-5 w-5" />
                                )}
                                {tab.name}
                            </div>
                        </button>
                    ))}
                </div>
            </div>

            <div className="p-6">
                {/* Shop Header */}
                <div className="text-center mb-4">
                    <div className="flex items-center justify-center gap-2 mb-3">
                        <Store className="h-6 w-6 text-blue-400" />
                        <h2 className="text-2xl font-bold text-white">
                            {singleShop.name}'s {capitaliseFirstLetter(singleShop.shopType).replace("Furniture", "Sunday")} Shop
                        </h2>
                    </div>
                    
                    {/* Trader Reputation */}
                    <div className="inline-flex items-center px-4 py-2 rounded-lg bg-[#1a2f4a] border border-[#2a4a7c]">
                        <TraderRep heartWidth="w-5" shopId={singleShop.id} />
                    </div>
                </div>

                {/* Shopkeeper Image */}
                <figure className="px-4 mb-4">
                    <img
                        className="rounded-xl w-full max-w-[200px] mx-auto"
                        src={displayMissingIcon(shopkeeperImageSmall(singleShop.name))}
                        alt={singleShop.name}
                    />
                </figure>

                {/* Cash Display - Mobile */}
                <div className="md:hidden mb-4">
                    <div className="p-3 rounded-lg border border-[#2a4a7c] bg-[#1a2f4a]">
                        <div className="text-xs text-gray-400 mb-1">Your Cash</div>
                        <div className="flex items-center gap-2">
                            <img className="h-5 w-auto" src={yenImg} alt="yen" />
                            <span className="text-lg font-bold text-white">{cash}</span>
                        </div>
                    </div>
                </div>

                {/* Back Button - Mobile */}
                <Link to={-1} className="md:hidden">
                    <button className="w-full py-2 px-4 rounded-lg text-gray-300 hover:text-white bg-[#1a2f4a] hover:bg-[#1a2f4a]/80 border border-[#2a4a7c] transition-all duration-200 flex items-center justify-center gap-2">
                        <ArrowLeft className="h-4 w-4" />
                        Back
                    </button>
                </Link>

                {/* Actions */}
                <div className="mt-4">
                    <Link to="/tasks" className="block">
                        <button className="w-full py-3 px-4 rounded-lg bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 flex items-center justify-center gap-2">
                            <ClipboardList className="h-5 w-5" />
                            View Tasks
                        </button>
                    </Link>
                </div>
            </div>
        </div>
    );
}

export default SingleShopkeeper;
