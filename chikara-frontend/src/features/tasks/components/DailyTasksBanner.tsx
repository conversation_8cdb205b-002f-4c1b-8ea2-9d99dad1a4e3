import useGetDailyQuests from "@/features/dailytask/api/useGetDailyQuests";
import { calculateTimeUntilMidnight } from "@/helpers/dateHelpers";
import { Clock } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

export default function DailyTasksBanner() {
    const { data: dailyQuests, isLoading } = useGetDailyQuests({
        select: (data) => data.slice(0, 3),
    });
    const completedDailyQuests = dailyQuests?.filter((quest) => quest.questStatus === "complete");
    const [timeUntilMidnight, setTimeUntilMidnight] = useState<string>("");

    useEffect(() => {
        setTimeUntilMidnight(calculateTimeUntilMidnight());

        const timer = setInterval(() => {
            setTimeUntilMidnight(calculateTimeUntilMidnight());
        }, 60000); // Update every minute

        return () => clearInterval(timer);
    }, []);

    return (
        <Link
            to="/dailies"
            className="alert alert-info shadow-lg hover:shadow-xl transition-shadow duration-200"
        >
            <Clock className="w-6 h-6" />
            <div className="flex-1">
                <h3 className="font-bold">Daily Tasks</h3>
                <div className="text-sm">
                    {!isLoading && dailyQuests?.length > 0 && (
                        <span className="font-semibold">
                            {completedDailyQuests?.length} / {dailyQuests?.length} completed
                        </span>
                    )}
                </div>
            </div>
            <div className="text-sm">
                <span className="countdown font-mono">
                    <span className="text-xs">Resets in {timeUntilMidnight}</span>
                </span>
            </div>
        </Link>
    );
}
