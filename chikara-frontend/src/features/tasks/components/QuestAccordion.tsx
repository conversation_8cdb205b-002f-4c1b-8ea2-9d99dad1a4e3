import QuestItem from "@/features/tasks/components/QuestItem";
import { getQuestStatus } from "@/features/tasks/hooks/useQuestSortAndFilter";
import type { InventoryItem } from "@/types/item";
import type { QuestWithProgress, QuestGiver } from "@/types/quest";
import React, { type Dispatch, type SetStateAction } from "react";

interface QuestAccordionProps {
    quests: QuestWithProgress[];
    questGivers: QuestGiver[];
    availableQuests?: QuestWithProgress[];
    inventory?: InventoryItem[];
    expandedQuests: number[];
    setExpandedQuests: Dispatch<SetStateAction<number[]>>;
    showStatusGroups?: boolean;
    isPinned?: boolean;
    title?: string;
    titleIcon?: React.ReactNode;
}

export default function QuestAccordion({
    quests,
    questGivers,
    availableQuests = [],
    inventory = [],
    expandedQuests,
    setExpandedQuests,
    showStatusGroups = true,
    isPinned = false,
    title,
    titleIcon,
}: QuestAccordionProps) {
    if (quests.length === 0) {
        return null;
    }

    const renderQuestItem = (quest: QuestWithProgress) => (
        <QuestItem
            key={quest.id}
            quest={quest}
            questGivers={questGivers}
            availableQuests={availableQuests}
            inventory={inventory}
            expandedQuests={expandedQuests}
            setExpandedQuests={setExpandedQuests}
            isPinned={isPinned}
        />
    );

    if (!showStatusGroups) {
        // Simple list without status grouping (for pinned quests)
        return (
            <div className="space-y-3">
                {title && (
                    <div className="flex items-center gap-2 mb-3">
                        {titleIcon}
                        <h4 className="text-lg font-bold text-primary">{title}</h4>
                    </div>
                )}
                <div className="space-y-3">{quests.map(renderQuestItem)}</div>
            </div>
        );
    }

    // Grouped by status
    return (
        <div className="space-y-6">
            {["ready_to_complete", "in_progress", "available", "complete"].map((statusGroup) => {
                const questsInGroup = quests.filter((q) => getQuestStatus(q) === statusGroup);

                if (questsInGroup.length === 0) return null;

                const statusConfig = {
                    ready_to_complete: {
                        title: "Ready to Complete",
                        color: "text-success",
                        bgColor: "bg-success/10",
                        borderColor: "border-success/30",
                        emoji: "✅"
                    },
                    in_progress: {
                        title: "In Progress",
                        color: "text-info",
                        bgColor: "bg-info/10",
                        borderColor: "border-info/30",
                        emoji: "⚡"
                    },
                    available: {
                        title: "Available",
                        color: "text-warning",
                        bgColor: "bg-warning/10",
                        borderColor: "border-warning/30",
                        emoji: "🎯"
                    },
                    complete: {
                        title: "Completed",
                        color: "text-success",
                        bgColor: "bg-success/10",
                        borderColor: "border-success/30",
                        emoji: "🏆"
                    }
                }[statusGroup];

                return (
                    <div key={statusGroup} className="space-y-3">
                        {/* Status group header */}
                        <div className={`divider ${statusConfig?.color}`}>
                            <span className="text-lg font-bold flex items-center gap-2">
                                <span className="text-2xl">{statusConfig?.emoji}</span>
                                {statusConfig?.title}
                            </span>
                        </div>

                        {/* Quests in this status group */}
                        <div className="space-y-3">{questsInGroup.map(renderQuestItem)}</div>
                    </div>
                );
            })}
        </div>
    );
}
