import { DisplayQuestGiver } from "@/features/tasks/components/DisplayQuestGiver";
import TaskDetails from "@/features/tasks/components/TaskDetails";
import { getObjectiveProgress } from "@/features/tasks/helpers/getObjectiveProgress";
import {
    getStoryQuestBorderStyle,
    getChapterDisplayText,
    getStoryQuestIcon,
} from "@/features/tasks/helpers/storyQuestHelpers";
import { getQuestStatus } from "@/features/tasks/hooks/useQuestSortAndFilter";
import { getObjectiveText } from "@/hooks/useGetQuestObjectiveText";
import { cn } from "@/lib/utils";
import type { InventoryItem } from "@/types/item";
import type { QuestObjectiveWithProgress, QuestWithProgress, QuestGiver } from "@/types/quest";
import { BadgePlus, ChevronDown, ChevronUp, BookOpen } from "lucide-react";
import { type Dispatch, type SetStateAction } from "react";

const getSpecificObjectiveProgress = (quest: QuestWithProgress, objectiveIndex = 0, inventory: InventoryItem[]) => {
    if (!quest.quest_progress || quest.quest_progress.length === 0) {
        return 0;
    }

    const objective = quest.quest_objective?.[objectiveIndex];
    if (!objective) return 0;

    const progressCount = getObjectiveProgress(objective, inventory);

    if (!progressCount) return 0;

    return Math.min(progressCount, objective.quantity || 0);
};

// Calculate overall quest progress across all objectives
const getOverallQuestProgress = (quest: QuestWithProgress, inventory: InventoryItem[]) => {
    if (!quest.quest_objective || quest.quest_objective.length === 0) {
        return {
            currentProgress: 0,
            totalRequired: 0,
            percentage: 0,
        };
    }

    let totalProgress = 0;
    let totalRequired = 0;

    quest.quest_objective.forEach((objective: QuestObjectiveWithProgress, index: number) => {
        totalRequired += objective.quantity || 0;
        totalProgress += getSpecificObjectiveProgress(quest, index, inventory);
    });

    return {
        currentProgress: totalProgress,
        totalRequired,
        percentage: totalRequired > 0 ? (totalProgress / totalRequired) * 100 : 0,
    };
};

const toggleQuestExpand = (
    questId: number,
    expandedQuests: number[],
    setExpandedQuests: Dispatch<SetStateAction<number[]>>
) => {
    if (expandedQuests.includes(questId)) {
        setExpandedQuests(expandedQuests.filter((id) => id !== questId));
    } else {
        setExpandedQuests([...expandedQuests, questId]);
    }
};

interface QuestItemProps {
    quest: QuestWithProgress;
    questGivers: QuestGiver[];
    availableQuests?: QuestWithProgress[];
    inventory?: InventoryItem[];
    expandedQuests: number[];
    setExpandedQuests: Dispatch<SetStateAction<number[]>>;
    isPinned?: boolean;
}

export default function QuestItem({
    quest,
    questGivers,
    availableQuests = [],
    inventory = [],
    expandedQuests,
    setExpandedQuests,
    isPinned = false,
}: QuestItemProps) {
    const status = getQuestStatus(quest);
    const isExpanded = expandedQuests.includes(quest.id);
    const hasMultipleObjectives = quest.quest_objective?.length > 1;
    const primaryObjective = quest.quest_objective?.[0];
    const {
        currentProgress,
        totalRequired,
        percentage: progressPercentage,
    } = getOverallQuestProgress(quest, inventory);

    const questGiver = questGivers?.find((giver) => giver.id === quest.shopId) || questGivers?.[0];

    const chapterText = getChapterDisplayText(quest);
    const storyIcon = getStoryQuestIcon(quest, status);
    const storyBorderStyle = getStoryQuestBorderStyle(quest, status);

    const cardColorClass = quest.isStoryQuest
        ? "card-border border-primary border-2"
        : isPinned
          ? "card-border border-accent border-2"
          : status === "available"
            ? "card-border border-warning"
            : status === "in_progress"
              ? "card-border border-info"
              : status === "ready_to_complete"
                ? "card-border border-success border-2"
                : "card-border border-base-300";

    return (
        <div
            key={quest.id}
            className={cn(
                "card bg-base-200 shadow-lg hover:shadow-xl transition-all duration-200",
                cardColorClass
            )}
        >
            {/* Quest Header */}
            <div
                className="card-body p-4 cursor-pointer"
                onClick={() => toggleQuestExpand(quest.id, expandedQuests, setExpandedQuests)}
            >
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        {/* Quest Giver Avatar */}
                        <div className="avatar">
                            <div className="w-12 rounded-full ring ring-base-300 ring-offset-base-100 ring-offset-2">
                                <DisplayQuestGiver src={questGiver} width={48} height={48} />
                            </div>
                        </div>

                        <div className="flex-1">
                            <div className="flex items-center gap-2">
                                {quest.isStoryQuest && storyIcon && <span className="text-lg">{storyIcon}</span>}
                                <h2
                                    className={cn(
                                        "card-title text-lg",
                                        quest.isStoryQuest ? "text-primary" : "text-base-content"
                                    )}
                                >
                                    {quest.name}
                                </h2>
                                {quest.isStoryQuest && <BookOpen className="w-4 h-4 text-primary" />}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                                {quest.isStoryQuest && chapterText ? (
                                    <span className="badge badge-primary badge-sm">{chapterText}</span>
                                ) : (
                                    <span className="badge badge-neutral badge-sm">{questGiver?.name}</span>
                                )}
                                {quest.levelReq > 1 && (
                                    <span className="badge badge-outline badge-sm">Lvl {quest.levelReq}+</span>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center gap-2">
                        {/* New Quests */}
                        {availableQuests?.includes(quest) && (
                            <div className="indicator">
                                <span className="indicator-item badge badge-secondary badge-sm">NEW</span>
                            </div>
                        )}

                        <button className="btn btn-circle btn-ghost btn-sm">
                            {isExpanded ? (
                                <ChevronUp className="w-5 h-5" />
                            ) : (
                                <ChevronDown className="w-5 h-5" />
                            )}
                        </button>
                    </div>
                </div>

                {/* Progress Bar (for in-progress quests) */}
                {status === "in_progress" && !isExpanded && (
                    <div className="mt-4">
                        <div className="flex justify-between text-sm mb-2">
                            {hasMultipleObjectives ? (
                                <>
                                    <span className="text-base-content/70">Multiple Objectives</span>
                                    <span className="badge badge-info badge-sm">
                                        {currentProgress}/{totalRequired}
                                    </span>
                                </>
                            ) : (
                                <>
                                    <span className="text-base-content/70">
                                        {primaryObjective ? getObjectiveText(primaryObjective) : "No objectives"}
                                    </span>
                                    <span className="badge badge-info badge-sm">
                                        {getSpecificObjectiveProgress(quest, 0, inventory)}/
                                        {primaryObjective?.quantity || 0}
                                    </span>
                                </>
                            )}
                        </div>
                        <progress 
                            className="progress progress-info" 
                            value={progressPercentage} 
                            max="100"
                        ></progress>
                    </div>
                )}
            </div>

            {/* Quest Details (Expanded) */}
            {isExpanded && (
                <TaskDetails
                    quest={quest}
                    objectives={quest.quest_objective}
                    overallProgress={{
                        currentProgress,
                        totalRequired,
                        progressPercentage,
                    }}
                />
            )}
        </div>
    );
}
