import { Slide<PERSON>H<PERSON>zon<PERSON>, ArrowUpDown, X } from "lucide-react";
/* eslint-disable no-unused-vars */
import { useMemo } from "react";
import type { QuestFilterOption, QuestSortOption, SortDirection } from "../hooks/useQuestSortAndFilter";
import type { QuestGiver } from "@/types/quest";

interface QuestSortAndFilterControlsProps {
    sortOption: QuestSortOption;
    setSortOption: (option: QuestSortOption) => void;
    sortDirection: SortDirection;
    setSortDirection: (direction: SortDirection) => void;
    filterOptions: QuestFilterOption;
    setFilterOptions: (options: QuestFilterOption) => void;
    questGivers: QuestGiver[];
    showFilters: boolean;
    setShowFilters: (show: boolean) => void;
    activeTab: string;
}

export default function QuestSortAndFilterControls({
    sortOption,
    setSortOption,
    sortDirection,
    setSortDirection,
    filterOptions,
    setFilterOptions,
    questGivers,
    showFilters,
    setShowFilt<PERSON>,
    activeTab,
}: QuestSortAndFilterControlsProps) {
    // Track if any filters are active
    const hasActiveFilters = useMemo(() => {
        return (
            (filterOptions.status && filterOptions.status !== "all") ||
            filterOptions.minLevel !== undefined ||
            filterOptions.maxLevel !== undefined ||
            filterOptions.questGiverId !== undefined ||
            filterOptions.hasItemRewards === true ||
            filterOptions.hasTalentPointRewards === true ||
            (filterOptions.location !== undefined && filterOptions.location !== "any")
        );
    }, [filterOptions]);

    // Clear all filters
    const clearAllFilters = () => {
        setFilterOptions({
            status: "all",
        });
    };

    // Toggle sort direction
    const setAscOrDesc = () => {
        setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    };

    return (
        <div className="space-y-4">
            {/* Filter Controls Bar */}
            <div className="card bg-base-200 shadow-md">
                <div className="card-body p-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <button
                                className="btn btn-sm btn-ghost gap-2"
                                onClick={() => setAscOrDesc()}
                            >
                                <ArrowUpDown className="w-4 h-4" />
                                <span className="font-mono uppercase">{sortOption.replace("_", " ")}</span>
                                <span className="badge badge-sm badge-neutral">
                                    {sortDirection === "asc" ? "↑" : "↓"}
                                </span>
                            </button>
                        </div>
                        <button
                            className={`btn btn-sm ${hasActiveFilters ? "btn-primary" : "btn-ghost"} gap-2`}
                            onClick={() => setShowFilters(!showFilters)}
                        >
                            <SlidersHorizontal className="w-4 h-4" />
                            Filters
                            {hasActiveFilters && <span className="badge badge-secondary badge-xs">Active</span>}
                        </button>
                    </div>
                </div>
            </div>

            {/* Filter Panel (Collapsible) */}
            {showFilters && (
                <div className="card bg-base-200 shadow-lg">
                    <div className="card-body">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="card-title text-lg">Filters & Sorting</h3>
                            {hasActiveFilters && (
                                <button
                                    className="btn btn-sm btn-ghost btn-error gap-2"
                                    onClick={clearAllFilters}
                                >
                                    <X className="w-4 h-4" />
                                    Clear All
                                </button>
                            )}
                        </div>

                        {/* Sort Options */}
                        <div className="form-control w-full">
                            <label className="label">
                                <span className="label-text">Sort By</span>
                            </label>
                            <select
                                value={sortOption}
                                className="select select-bordered select-sm w-full"
                                onChange={(e) => setSortOption(e.target.value as QuestSortOption)}
                            >
                                <option value="level">Level</option>
                                <option value="name">Name</option>
                                <option value="Yen_reward">Yen Reward</option>
                                <option value="XP_reward">XP Reward</option>
                                <option value="REP_reward">REP Reward</option>
                            </select>
                        </div>

                        <div className="divider text-sm">Filter Options</div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Status Filter */}
                            <div className="form-control w-full">
                                <label className="label">
                                    <span className="label-text">Status</span>
                                </label>
                                <select
                                    disabled={activeTab === "complete"}
                                    value={filterOptions.status || "all"}
                                    className="select select-bordered select-sm w-full"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            status: e.target.value as
                                                | "available"
                                                | "in_progress"
                                                | "complete"
                                                | "ready_to_complete"
                                                | "all",
                                        })
                                    }
                                >
                                    <option value="all">
                                        {activeTab === "complete" ? "Completed" : "All Statuses"}
                                    </option>
                                    <option value="available">New</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="ready_to_complete">Ready to turn in</option>
                                </select>
                            </div>

                            {/* Quest Giver Filter */}
                            <div className="form-control w-full">
                                <label className="label">
                                    <span className="label-text">Quest Giver</span>
                                </label>
                                <select
                                    value={filterOptions.questGiverId || ""}
                                    className="select select-bordered select-sm w-full"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            questGiverId: e.target.value ? Number.parseInt(e.target.value) : undefined,
                                        })
                                    }
                                >
                                    <option value="">All Givers</option>
                                    {questGivers &&
                                        questGivers
                                            .filter((giver) => giver.shopType !== "gang")
                                            .map((giver) => (
                                                <option key={giver.id} value={giver.id}>
                                                    {giver.name}
                                                </option>
                                            ))}
                                </select>
                            </div>

                            {/* Location Filter */}
                            <div className="form-control w-full">
                                <label className="label">
                                    <span className="label-text">Location</span>
                                </label>
                                <select
                                    value={filterOptions.location || "any"}
                                    className="select select-bordered select-sm w-full"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            location: e.target.value as
                                                | "church"
                                                | "shrine"
                                                | "mall"
                                                | "alley"
                                                | "school"
                                                | "sewers"
                                                | "themepark"
                                                | "any",
                                        })
                                    }
                                >
                                    <option value="any">Any Location</option>
                                    <option value="church">Church</option>
                                    <option value="shrine">Shrine</option>
                                    <option value="mall">Mall</option>
                                    <option value="alley">Alley</option>
                                    <option value="school">School</option>
                                    <option value="sewers">Sewers</option>
                                </select>
                            </div>

                            {/* Level Range */}
                            <div className="form-control w-full">
                                <label className="label">
                                    <span className="label-text">Level Range</span>
                                    {(filterOptions.minLevel !== undefined || filterOptions.maxLevel !== undefined) && (
                                        <button
                                            className="btn btn-ghost btn-xs"
                                            onClick={() =>
                                                setFilterOptions({
                                                    ...filterOptions,
                                                    minLevel: undefined,
                                                    maxLevel: undefined,
                                                })
                                            }
                                        >
                                            Reset
                                        </button>
                                    )}
                                </label>
                                <div className="join">
                                    <input
                                        type="number"
                                        placeholder="Min"
                                        value={filterOptions.minLevel || ""}
                                        min="1"
                                        className="input input-bordered input-sm join-item w-1/2"
                                        onChange={(e) =>
                                            setFilterOptions({
                                                ...filterOptions,
                                                minLevel: e.target.value ? Number.parseInt(e.target.value) : undefined,
                                            })
                                        }
                                    />
                                    <input
                                        type="number"
                                        placeholder="Max"
                                        value={filterOptions.maxLevel || ""}
                                        min="1"
                                        className="input input-bordered input-sm join-item w-1/2"
                                        onChange={(e) =>
                                            setFilterOptions({
                                                ...filterOptions,
                                                maxLevel: e.target.value ? Number.parseInt(e.target.value) : undefined,
                                            })
                                        }
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Checkboxes */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div className="form-control">
                                <label className="label cursor-pointer">
                                    <span className="label-text">Has Item Rewards</span>
                                    <input
                                        type="checkbox"
                                        checked={filterOptions.hasItemRewards === true}
                                        className="checkbox checkbox-primary"
                                        onChange={(e) =>
                                            setFilterOptions({
                                                ...filterOptions,
                                                hasItemRewards: e.target.checked || undefined,
                                            })
                                        }
                                    />
                                </label>
                            </div>
                            <div className="form-control">
                                <label className="label cursor-pointer">
                                    <span className="label-text">Has Talent Point Rewards</span>
                                    <input
                                        type="checkbox"
                                        checked={filterOptions.hasTalentPointRewards === true}
                                        className="checkbox checkbox-primary"
                                        onChange={(e) =>
                                            setFilterOptions({
                                                ...filterOptions,
                                                hasTalentPointRewards: e.target.checked || undefined,
                                            })
                                        }
                                    />
                                </label>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="card-actions justify-end mt-6">
                            <button
                                className="btn btn-ghost btn-sm"
                                onClick={() => setShowFilters(false)}
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
