import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

export default function StoryModeBanner() {
    return (
        <div className="hero bg-gradient-to-br from-primary/20 to-secondary/20 rounded-box shadow-lg">
            <div className="hero-content p-4">
                <div className="flex items-center gap-4 w-full">
                    <div className="avatar">
                        <div className="w-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            <div className="bg-gradient-to-br from-primary to-secondary w-full h-full flex items-center justify-center">
                                <BookOpen className="w-8 h-8 text-primary-content" />
                            </div>
                        </div>
                    </div>
                    <div className="flex-1">
                        <h1 className="text-2xl font-bold text-primary">Main Story Mode</h1>
                        <p className="text-base-content/70">Follow the academy's narrative through epic story quests</p>
                    </div>
                    <div className="badge badge-accent badge-lg gap-2">
                        <Star className="w-4 h-4" />
                        Auto-starting
                    </div>
                </div>
            </div>
        </div>
    );
}
