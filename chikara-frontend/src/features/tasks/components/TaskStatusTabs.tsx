import type { QuestFilterOption } from "@/features/tasks/hooks/useQuestSortAndFilter";
import { BookO<PERSON>, CheckCircle2, ListTodo } from "lucide-react";

type QuestTab = "current" | "completed" | "story";

interface TaskStatusTabsProps {
    activeTab: QuestTab;
    setActiveTab: (_activeTab: QuestTab) => void;
    currentQuestLength: number;
    completedQuestLength: number;
    storyQuestLength: number;
    setFilterOptions: (_options: QuestFilterOption) => void;
}

export default function TaskStatusTabs({
    activeTab,
    setActiveTab,
    currentQuestLength,
    completedQuestLength,
    storyQuestLength,
    setFilterOptions,
}: TaskStatusTabsProps) {
    return (
        <div role="tablist" className="tabs tabs-boxed bg-base-200 p-1">
            <button
                role="tab"
                className={`tab tab-lg gap-2 ${activeTab === "story" ? "tab-active !bg-primary !text-primary-content" : ""}`}
                onClick={() => {
                    setActiveTab("story");
                    setFilterOptions({ status: "all" });
                }}
            >
                <BookOpen className="w-4 h-4" />
                <span className="font-semibold">Main Story</span>
                <span className="badge badge-sm badge-secondary">{storyQuestLength}</span>
            </button>

            <button
                role="tab"
                className={`tab tab-lg gap-2 ${activeTab === "current" ? "tab-active !bg-warning !text-warning-content" : ""}`}
                onClick={() => {
                    setActiveTab("current");
                    setFilterOptions({ status: "all" });
                }}
            >
                <ListTodo className="w-4 h-4" />
                <span className="font-semibold">Current</span>
                <span className="badge badge-sm badge-warning">{currentQuestLength}</span>
            </button>

            <button
                role="tab"
                className={`tab tab-lg gap-2 ${activeTab === "completed" ? "tab-active !bg-success !text-success-content" : ""}`}
                onClick={() => {
                    setActiveTab("completed");
                    setFilterOptions({ status: "all" });
                }}
            >
                <CheckCircle2 className="w-4 h-4" />
                <span className="font-semibold">Completed</span>
                <span className="badge badge-sm badge-success">{completedQuestLength}</span>
            </button>
        </div>
    );
}
