import { Plus, Minus } from "lucide-react";

export default function TrainingIntensitySelector({
    value,
    onChange,
    valueId,
}: {
    value: number;
    onChange: (_value: number, _valueId: string) => void;
    valueId: string;
}) {
    const minValue = 1;
    const maxValue = 50;

    const handleIncrement = () => {
        if (value < maxValue) {
            onChange(value + 1, valueId);
        }
    };

    const handleDecrement = () => {
        if (value > minValue) {
            onChange(value - 1, valueId);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = parseInt(e.target.value) || minValue;
        const clampedValue = Math.max(minValue, Math.min(maxValue, newValue));
        onChange(clampedValue, valueId);
    };

    return (
        <div className="space-y-2">
            <div className="flex items-center justify-between">
                <label className="text-xs font-bold text-secondary uppercase tracking-wider">Intensity</label>
            </div>

            {/* Training Intensity Controls */}
            <div className="join w-full">
                <button
                    type="button"
                    disabled={value <= minValue}
                    className="btn btn-sm join-item btn-secondary"
                    onClick={handleDecrement}
                >
                    <Minus className="w-4 h-4" />
                </button>

                <input
                    type="number"
                    min={minValue}
                    max={maxValue}
                    value={value}
                    className="input input-sm input-bordered join-item flex-1 text-center font-bold text-primary"
                    onChange={handleInputChange}
                />

                <button
                    type="button"
                    disabled={value >= maxValue}
                    className="btn btn-sm join-item btn-secondary"
                    onClick={handleIncrement}
                >
                    <Plus className="w-4 h-4" />
                </button>
            </div>

            {/* Quick Select Buttons */}
            <div className="join w-full">
                {[1, 5, 10, 25, 50].map((quickValue) => (
                    <button
                        key={quickValue}
                        type="button"
                        className={`btn btn-xs join-item flex-1 ${
                            value === quickValue
                                ? "btn-accent"
                                : "btn-ghost"
                        }`}
                        onClick={() => onChange(quickValue, valueId)}
                    >
                        {quickValue}
                    </button>
                ))}
            </div>
        </div>
    );
}
