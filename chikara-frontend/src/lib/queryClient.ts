import { ForbiddenError, MaintenanceModeError, UnauthorizedError } from "@/helpers/apiError";
import handleLogout from "@/helpers/handleLogout";
import { QueryClient, QueryCache, MutationCache } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const handleGlobalError = (error: unknown) => {
    if (error instanceof UnauthorizedError) {
        handleLogout();
    } else if (error instanceof ForbiddenError) {
        console.error("Forbidden Error:", error);
        toast.error("You do not have permission to perform this action.");
    } else if (error instanceof MaintenanceModeError) {
        toast.error(error.message, { duration: Number.POSITIVE_INFINITY });
    } else if (error instanceof Error) {
        console.error("Unhandled Query/Mutation Error:", error);
        toast.error(`An error occurred: ${error.message}`);
    } else {
        console.error("Unknown Query/Mutation Error:", error);
        toast.error("An unknown error occurred.");
    }
};

export const createQueryClient = () => {
    return new QueryClient({
        queryCache: new QueryCache({
            onError: handleGlobalError,
        }),
        mutationCache: new MutationCache({
            onError: handleGlobalError,
        }),
        defaultOptions: {
            queries: {
                retry: (failureCount, error) => {
                    // Don't retry on 401, 403, or maintenance errors
                    if (
                        error instanceof UnauthorizedError ||
                        error instanceof ForbiddenError ||
                        error instanceof MaintenanceModeError
                    ) {
                        return false;
                    }
                    return failureCount < 3;
                },
                refetchOnReconnect: "always",
                staleTime: 10000, // 10 seconds default
                throwOnError: (error: Error & { response?: { status: number } }) =>
                    (error.response?.status ?? 0) >= 500,
            },
        },
    });
};

// type QueryKeyType = [string, ...unknown[]];

// export const defaultQueryFn: QueryFunction<unknown, QueryKeyType> = async ({ queryKey }) => {
//     const [url, ...params] = queryKey;

//     // If there are additional params, append them as query parameters
//     const queryString =
//         params.length > 0 ? `?${new URLSearchParams(params[0] as Record<string, string>).toString()}` : "";
//     try {
//         return await handleGet<unknown>(`${url}${queryString}`);
//     } catch (e) {
//         handleGlobalError(e);
//         return null;
//     }
// };
