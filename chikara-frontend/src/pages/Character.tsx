/* eslint-disable no-use-before-define */
import SkillTalentTree from "@/features/user/components/SkillTalentTree";
import type { CraftingSkill } from "@/features/user/types/talents";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGetUserSkills from "@/hooks/api/useGetUserSkills";
import { cn } from "@/lib/utils";
import EquippedAbilities from "@/features/user/components/EquippedAbilities";
import EquippedItemsDisplay from "@/features/user/components/EquippedItemsDisplay";
import {
    ArrowLeft,
    Brain,
    Cpu,
    Dumbbell,
    FlaskRoundIcon as Flask,
    Footprints,
    Hammer,
    Heart,
    Pickaxe,
    Shield,
    Zap,
    Swords,
    Activity,
    Wallet,
    Star,
    Battery,
    TrendingUp,
    Coins,
} from "lucide-react";
import type React from "react";
import { useState } from "react";

type GatheringSkill = "mining" | "scavenging" | "foraging";

export default function CharacterPanel() {
    const { data: userSkills } = useGetUserSkills();
    const { data: currentUser } = useFetchCurrentUser();
    const [selectedCraftingSkill, setSelectedCraftingSkill] = useState<CraftingSkill | null>(null);
    const [selectedGatheringSkill, setSelectedGatheringSkill] = useState<GatheringSkill | null>(null);

    // Calculate progress percentages
    const xpProgress = currentUser?.xpForNextLevel ? (currentUser.xp / currentUser.xpForNextLevel) * 100 : 0;
    const healthProgress = currentUser?.health ? (currentUser.currentHealth / currentUser.health) * 100 : 0;
    const energyProgress = currentUser?.energy ? Math.min((currentUser.energy / 100) * 100, 100) : 0; // Clamped at 100%
    const apProgress = currentUser?.maxActionPoints
        ? (currentUser.actionPoints / currentUser.maxActionPoints) * 100
        : 0;

    // Handle crafting skills view
    if (selectedCraftingSkill) {
        return (
            <div className="max-w-(--breakpoint-md) mx-auto h-full overflow-hidden flex flex-col p-4">
                <button
                    type="button"
                    className="btn btn-secondary btn-sm gap-2 mb-4"
                    onClick={() => {
                        setSelectedCraftingSkill(null);
                    }}
                    style={{"--depth": 1} as React.CSSProperties}
                >
                    <ArrowLeft className="size-4" />
                    Back to Character
                </button>
                <SkillTalentTree selectedSkill={selectedCraftingSkill} />
            </div>
        );
    }

    // Handle gathering skills view
    if (selectedGatheringSkill) {
        return (
            <div className="max-w-(--breakpoint-md) mx-auto h-full overflow-hidden flex flex-col p-4">
                <button
                    type="button"
                    className="btn btn-success btn-sm gap-2 mb-4"
                    onClick={() => {
                        setSelectedGatheringSkill(null);
                    }}
                    style={{"--depth": 1} as React.CSSProperties}
                >
                    <ArrowLeft className="size-4" />
                    Back to Character
                </button>
                <div className="card bg-base-200 shadow-xl h-full md:h-[75vh] overflow-hidden flex flex-col" style={{"--depth": 1, "--noise": 1} as React.CSSProperties}>
                    <div className="card-body p-4">
                        <div className="flex items-center justify-between mb-4">
                            <div>
                                <h3 className="text-2xl font-bold text-success capitalize">{selectedGatheringSkill} Details</h3>
                                <div className="text-sm text-base-content/60">
                                    View and manage your {selectedGatheringSkill} skill
                                </div>
                            </div>
                            <div className="badge badge-success badge-lg">
                                Level {userSkills?.[selectedGatheringSkill]?.level || 0}
                            </div>
                        </div>
                        <div className="bg-base-300/50 rounded-lg p-4 grow overflow-y-auto">
                            <p className="text-base-content/70 mb-4">
                                This panel would show detailed information about your {selectedGatheringSkill} skill,
                                including:
                            </p>
                            <ul className="list-disc pl-5 space-y-2 text-base-content">
                                <li>Skill bonuses and benefits</li>
                                <li>Available gathering locations</li>
                                <li>Resource types and rarity chances</li>
                                <li>Special abilities unlocked at certain levels</li>
                                <li>Tools and equipment that improve efficiency</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full overflow-y-auto p-4 max-w-7xl mx-auto">
            {/* Header with user info */}
            <div className="card card-border bg-base-200 shadow-xl mb-6" style={{"--depth": 1, "--noise": 1} as React.CSSProperties}>
                <div className="card-body p-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            {currentUser?.avatar && (
                                <div className="avatar">
                                    <div className="w-20 mask mask-hexagon">
                                        <img src={currentUser.avatar} alt={currentUser.username} />
                                    </div>
                                </div>
                            )}
                            <div>
                                <h2 className="text-3xl font-bold text-primary">{currentUser?.username}</h2>
                                <div className="flex items-center gap-2 mt-1">
                                    <span className="badge badge-secondary badge-outline">
                                        Level {currentUser?.level}
                                    </span>
                                    <span className="badge badge-accent badge-soft">
                                        {currentUser?.class}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className="text-right">
                            <div className="stat-value text-warning flex items-center gap-2 justify-end">
                                <Coins className="size-6" />
                                <span className="text-2xl">${currentUser?.cash.toLocaleString()}</span>
                            </div>
                            <div className="text-success flex items-center gap-2 justify-end mt-1">
                                <Wallet className="size-4" />
                                <span className="text-sm">Bank: ${currentUser?.bank_balance.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column */}
                <div className="space-y-6">
                    {/* Character Stats */}
                    <div className="card bg-base-200 shadow-lg" style={{"--depth": 1, "--noise": 1} as React.CSSProperties}>
                        <div className="card-body p-4">
                            <h3 className="card-title text-primary flex items-center gap-2">
                                <Activity className="size-5" />
                                Character Stats
                            </h3>
                            <div className="space-y-4 mt-4">
                                <ProgressStat
                                    label="Health"
                                    current={currentUser?.currentHealth || 0}
                                    max={currentUser?.health || 1}
                                    progress={healthProgress}
                                    colorClass="progress-error"
                                    icon={<Heart className="size-5 text-error" />}
                                />

                                <ProgressStat
                                    label="Energy"
                                    current={currentUser?.energy || 0}
                                    max={100}
                                    progress={energyProgress}
                                    colorClass="progress-info"
                                    icon={<Battery className="size-5 text-info" />}
                                />
                                <ProgressStat
                                    label="Action Points"
                                    current={currentUser?.actionPoints || 0}
                                    max={currentUser?.maxActionPoints || 1}
                                    progress={apProgress}
                                    colorClass="progress-success"
                                    icon={<TrendingUp className="size-5 text-success" />}
                                />
                                <ProgressStat
                                    label="Experience"
                                    current={currentUser?.xp || 0}
                                    max={currentUser?.xpForNextLevel || 1}
                                    progress={xpProgress}
                                    colorClass="progress-secondary"
                                    icon={<Star className="size-5 text-secondary" />}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Primary Attributes */}
                    <div className="card bg-base-200 shadow-lg" style={{"--depth": 1, "--noise": 1} as React.CSSProperties}>
                        <div className="card-body p-4">
                            <h3 className="card-title text-accent flex items-center gap-2">
                                <Swords className="size-5" />
                                Attributes
                            </h3>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-4">
                                <StatCard
                                    icon={<Dumbbell className="size-5 text-warning" />}
                                    name="Strength"
                                    value={userSkills?.strength.level ?? 0}
                                />
                                <StatCard
                                    icon={<Shield className="size-5 text-info" />}
                                    name="Defence"
                                    value={userSkills?.defence.level ?? 0}
                                />
                                <StatCard
                                    icon={<Footprints className="size-5 text-success" />}
                                    name="Dexterity"
                                    value={userSkills?.dexterity.level ?? 0}
                                />
                                <StatCard
                                    icon={<Brain className="size-5 text-secondary" />}
                                    name="Intelligence"
                                    value={userSkills?.intelligence.level ?? 0}
                                />
                                <StatCard
                                    icon={<Zap className="size-5 text-accent" />}
                                    name="Endurance"
                                    value={userSkills?.endurance.level ?? 0}
                                />
                                <StatCard
                                    icon={<Heart className="size-5 text-error" />}
                                    name="Vitality"
                                    value={userSkills?.vitality.level ?? 0}
                                />
                            </div>
                        </div>
                    </div>
                    {/* Equipped Abilities */}
                    <EquippedAbilities />
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                    {/* Equipped Items */}
                    <div className="card bg-base-200 shadow-lg" style={{"--depth": 1, "--noise": 1} as React.CSSProperties}>
                        <div className="card-body p-4">
                            <h3 className="card-title text-info flex items-center gap-2">
                                <Shield className="size-5" />
                                Equipment
                            </h3>
                            <div className="mt-4">
                                <EquippedItemsDisplay />
                            </div>
                        </div>
                    </div>

                    {/* Skills */}
                    <div className="card bg-base-200 shadow-lg" style={{"--depth": 1, "--noise": 1} as React.CSSProperties}>
                        <div className="card-body p-4">
                            <h3 className="card-title text-secondary flex items-center gap-2">
                                <Hammer className="size-5" />
                                Skills
                            </h3>
                            <div className="flex flex-col gap-4 lg:flex-row lg:gap-6 mt-4">
                                <div className="space-y-3 flex-1">
                                    <div className="divider divider-success text-sm m-0">
                                        <div className="flex items-center gap-2">
                                            <Pickaxe className="size-4" /> 
                                            <span className="font-medium">Gathering</span>
                                        </div>
                                    </div>

                                <SkillCard
                                    name="Mining"
                                    skill={userSkills?.mining}
                                    icon={<Pickaxe className="size-4 text-gray-400" />}
                                    onClick={() => setSelectedGatheringSkill("mining")}
                                />
                                <SkillCard
                                    name="Scavenging"
                                    skill={userSkills?.scavenging}
                                    icon={<Search className="size-4 text-green-400" />}
                                    onClick={() => setSelectedGatheringSkill("scavenging")}
                                />
                                <SkillCard
                                    name="Foraging"
                                    skill={userSkills?.foraging}
                                    icon={<Leaf className="size-4 text-emerald-400" />}
                                    onClick={() => setSelectedGatheringSkill("foraging")}
                                />
                            </div>
                            <div className="space-y-2 flex-1 lg:space-y-4">
                                <div className="border-t border-gray-700 pt-2 mt-4 lg:border-t-0 lg:mt-0 lg:pt-0">
                                    <h4 className="text-purple-300 text-sm mb-2 flex items-center">
                                        <Hammer className="size-4 mr-1" /> Crafting
                                    </h4>
                                </div>

                                <SkillCard
                                    name="Fabrication"
                                    skill={userSkills?.fabrication}
                                    icon={<Hammer className="size-4 text-orange-400" />}
                                    onClick={() => setSelectedCraftingSkill("fabrication")}
                                />
                                <SkillCard
                                    name="Outfitting"
                                    skill={userSkills?.outfitting}
                                    icon={<Shirt className="size-4 text-blue-400" />}
                                    onClick={() => setSelectedCraftingSkill("outfitting")}
                                />
                                <SkillCard
                                    name="Chemistry"
                                    skill={userSkills?.chemistry}
                                    icon={<Flask className="size-4 text-purple-400" />}
                                    onClick={() => setSelectedCraftingSkill("chemistry")}
                                />
                                <SkillCard
                                    name="Electronics"
                                    skill={userSkills?.electronics}
                                    icon={<Cpu className="size-4 text-cyan-400" />}
                                    onClick={() => setSelectedCraftingSkill("electronics")}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

interface ProgressStatProps {
    label: string;
    current: number;
    max: number;
    progress: number;
    colorClass: string;
    icon: React.ReactNode;
}

function ProgressStat({ label, current, max, progress, colorClass, icon }: ProgressStatProps) {
    return (
        <div className="space-y-2">
            <div className="flex items-center justify-between">
                <span className="text-base-content/70 flex items-center gap-2 font-medium">
                    {icon}
                    {label}
                </span>
                <span className="text-base-content font-bold">
                    {current.toLocaleString()} / {max.toLocaleString()}
                </span>
            </div>
            <progress 
                className={cn("progress", colorClass)} 
                value={progress} 
                max={100}
                style={{"--depth": 1} as React.CSSProperties}
            ></progress>
        </div>
    );
}

interface StatCardProps {
    icon: React.ReactNode;
    name: string;
    value: number;
}

function StatCard({ icon, name, value }: StatCardProps) {
    return (
        <div className="stats bg-base-300 shadow-md" style={{"--depth": 1} as React.CSSProperties}>
            <div className="stat p-3">
                <div className="stat-figure text-primary">
                    {icon}
                </div>
                <div className="stat-title text-xs">{name}</div>
                <div className="stat-value text-lg">{value}</div>
            </div>
        </div>
    );
}

interface SkillCardProps {
    name: string;
    skill: any;
    icon: React.ReactNode;
    onClick?: () => void;
}

function SkillCard({ name, skill, icon, onClick }: SkillCardProps) {
    const percentage = (skill?.experience / skill?.expToNextLevel) * 100 || 0;
    const isCraftingSkill = ["fabrication", "outfitting", "chemistry", "electronics"].includes(name.toLowerCase());
    const isGatheringSkill = ["mining", "scavenging", "foraging"].includes(name.toLowerCase());
    const hasTalentPoints = skill?.talentPoints > 0;

    return (
        <div
            role={onClick ? "button" : undefined}
            tabIndex={onClick ? 0 : undefined}
            aria-label={onClick ? `View ${name} skill details` : undefined}
            className={cn(
                "bg-gray-900/50 rounded-lg p-2 relative",
                onClick && "hover:bg-gray-800/70 transition-colors cursor-pointer",
                isCraftingSkill && onClick && "border border-gray-700 hover:border-purple-500",
                isGatheringSkill && onClick && "border border-gray-700 hover:border-green-500",
                hasTalentPoints && "from-yellow-950/20 to-transparent bg-linear-to-r"
            )}
            onClick={onClick}
            onKeyDown={(e) => {
                if (onClick && (e.key === "Enter" || e.key === " ")) {
                    e.preventDefault();
                    onClick();
                }
            }}
        >
            {/* Pulsing indicator for available talent points */}
            {hasTalentPoints && (
                <div className="absolute -top-1 -right-1 flex size-3">
                    <span className="animate-ping absolute inline-flex size-full rounded-full bg-yellow-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full size-3 bg-yellow-500"></span>
                </div>
            )}

            <div className="flex justify-between items-start mb-1">
                <div className="flex items-center gap-2">
                    <div
                        className={cn(
                            "size-7 rounded-full flex items-center justify-center",
                            isCraftingSkill ? "bg-gray-800" : isGatheringSkill ? "bg-gray-800/80" : "bg-gray-800"
                        )}
                    >
                        {icon}
                    </div>
                    <div>
                        <h4 className="text-white text-sm font-medium">{name}</h4>
                        <div className="flex items-center gap-1 text-xs">
                            <span
                                className={cn(
                                    isCraftingSkill
                                        ? "text-purple-300"
                                        : isGatheringSkill
                                          ? "text-green-300"
                                          : "text-purple-300"
                                )}
                            >
                                Level {skill?.level}
                            </span>
                        </div>
                    </div>
                </div>
                <span className="text-gray-300 text-xs absolute bottom-3 left-3">
                    {skill?.experience?.toLocaleString()}/{skill?.expToNextLevel?.toLocaleString()} XP
                </span>
            </div>
            <div className="w-full h-1.5 bg-gray-800 rounded-full overflow-hidden">
                <div
                    style={{ width: `${percentage}%` }}
                    className={cn(
                        "h-full rounded-full",
                        isCraftingSkill ? "bg-purple-600" : isGatheringSkill ? "bg-green-600" : "bg-purple-600"
                    )}
                ></div>
            </div>

            {/* Talent points badge */}
            {skill?.talentPoints > 0 && (
                <div className="absolute top-2 right-3 bg-yellow-900/70 px-2 py-0 flex items-center justify-center rounded-full border border-yellow-500/50">
                    <span className="text-xs text-yellow-300 font-medium">
                        {skill.talentPoints} {skill.talentPoints === 1 ? "point" : "points"}
                    </span>
                </div>
            )}

            {onClick && (
                <div className={cn("mt-2 text-xs text-right")}>
                    {hasTalentPoints ? (
                        <span className="text-yellow-300">Talent points available!</span>
                    ) : (
                        <span className={cn(isGatheringSkill ? "text-green-400" : "text-purple-400")}>
                            View talent tree
                        </span>
                    )}
                    <span
                        className={cn(
                            "ml-1",
                            isGatheringSkill ? "text-green-400" : "text-purple-400",
                            hasTalentPoints && "text-yellow-300"
                        )}
                    >
                        →
                    </span>
                </div>
            )}
        </div>
    );
}

// Custom icon components
function Search({ className }: { className?: string }) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={className}
        >
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.3-4.3" />
        </svg>
    );
}

function Leaf({ className }: { className?: string }) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={className}
        >
            <path d="M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z" />
            <path d="M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12" />
        </svg>
    );
}

function Shirt({ className }: { className?: string }) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={className}
        >
            <path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" />
        </svg>
    );
}
