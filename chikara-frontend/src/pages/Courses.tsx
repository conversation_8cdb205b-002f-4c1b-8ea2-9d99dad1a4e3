import greenTickImg from "@/assets/icons/UI/greenTick.png";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useCourseList, useStartCourse } from "@/features/course/api";
import { formatDistanceToNowStrict } from "date-fns";
import toast from "react-hot-toast";
import { formatCurrency } from "@/utils/currencyHelpers";
import type { User } from "@/types/user";
import { BookOpen, Clock, Coins, TrendingUp, Award, GraduationCap } from "lucide-react";

const convertToDays = (milliseconds: number): number => {
    const days = Math.floor(milliseconds / (24 * 60 * 60 * 1000));
    return days;
};

interface Course {
    id: number;
    name: string;
    stat: string;
    amount: number;
    time: number;
    cost: number;
    completed?: boolean;
}

interface GroupedCourses {
    stat: string;
    coursesData: Course[];
}

function Courses() {
    const { isLoading, data } = useCourseList();
    const { data: currentUser } = useFetchCurrentUser();

    const startCourseMutation = useStartCourse();

    const handleStartCourse = (courseId: number) => {
        if (currentUser?.activeCourseId && currentUser.activeCourseId > 0) {
            toast.error("You're already on a course!");
            return;
        }

        const selectedCourse = data?.find((course) => course.id === courseId);
        if (selectedCourse && selectedCourse.cost > (currentUser?.cash || 0)) {
            toast.error("You don't have enough cash to start this course!");
            return;
        }

        startCourseMutation.mutate({ courseId });
    };

    const groupedObjects = data?.reduce((acc: Record<string, Course[]>, obj: Course) => {
        const key = obj.stat;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    const groupedArray: GroupedCourses[] = [];
    for (const key in groupedObjects) {
        groupedArray.push({ stat: key, coursesData: groupedObjects[key] });
    }

    if (isLoading) return (
        <div className="flex justify-center items-center min-h-screen">
            <span className="loading loading-spinner loading-lg text-primary"></span>
        </div>
    );
    if ((currentUser?.level || 0) < 8) return null;

    const currentCourse = data?.find((course) => course.id === currentUser?.activeCourseId);

    return (
        <div className="min-h-screen text-white">
            {/* Header */}
            <div className="border-b border-base-300 bg-gradient-to-b from-base-100 to-base-200">
                <div className="max-w-7xl mx-auto px-4 py-6">
                    <div className="flex items-center gap-3 mb-4">
                        <GraduationCap className="w-8 h-8 text-primary" />
                        <h1 className="text-3xl font-bold text-base-content">Training Courses</h1>
                    </div>
                    
                    {/* Current Course Alert */}
                    {currentCourse && (
                        <div className="alert alert-info alert-soft">
                            <BookOpen className="w-5 h-5" />
                            <div>
                                <h3 className="font-semibold">Active Course: {currentCourse.name}</h3>
                                <p className="text-sm opacity-90">
                                    Ends in {formatDistanceToNowStrict(Number.parseInt(currentUser?.courseEnds || "0"))}
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 py-6">
                <div className="space-y-8">
                    {groupedArray?.map((courses) => (
                        <div key={courses.stat} className="space-y-4">
                            {/* Stat Header */}
                            <div className="flex items-center gap-3 p-3 rounded-lg border border-base-300 bg-base-200/50">
                                <Award className="w-5 h-5 text-primary" />
                                <div>
                                    <h2 className="text-lg font-bold text-base-content">
                                        {capitaliseFirstLetter(courses.stat)} Courses
                                    </h2>
                                    <p className="text-xs text-base-content/70">
                                        Enhance your {courses.stat} abilities
                                    </p>
                                </div>
                            </div>
                            
                            {/* Course Cards */}
                            <CoursesTable
                                courses={courses.coursesData}
                                handleStartCourse={handleStartCourse}
                                currentUser={currentUser}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

const getShortStat = (stat: string): string => {
    switch (stat) {
        case "endurance":
            return "END";
        case "intelligence":
            return "INT";
        case "dexterity":
            return "DEX";
        case "strength":
            return "STR";
        case "vitality":
            return "VIT";
        case "defence":
            return "DEF";

        default:
            return "";
    }
};

const courseStatusIcon = () => {};

export default Courses;

interface CoursesTableProps {
    courses: Course[];
    handleStartCourse: (courseId: number) => void;
    currentUser: User | undefined;
}

const CoursesTable = ({ courses, handleStartCourse, currentUser }: CoursesTableProps) => {
    return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {courses?.map((course) => {
                const isActive = course.id === currentUser?.activeCourseId;
                const isCompleted = course.completed;
                const canEnroll = !currentUser?.activeCourseId || currentUser?.activeCourseId === 0;
                const canAfford = course.cost <= (currentUser?.cash || 0);

                return (
                    <div 
                        key={course.id} 
                        className="card bg-base-200 border border-base-300 hover:border-primary/50 transition-all duration-300"
                    >
                        <div className="card-body">
                            {/* Course Title */}
                            <h3 className="card-title text-lg text-base-content">
                                {course.name}
                            </h3>
                            
                            {/* Stats Grid */}
                            <div className="grid grid-cols-2 gap-3 my-4">
                                {/* Stat Gain */}
                                <div className="stat bg-base-300 rounded-lg p-3">
                                    <div className="stat-title text-xs opacity-70">Stat Gain</div>
                                    <div className="stat-value text-lg text-success">
                                        +{course.amount}
                                    </div>
                                    <div className="stat-desc text-xs text-primary">
                                        {getShortStat(course.stat)}
                                    </div>
                                </div>
                                
                                {/* Duration */}
                                <div className="stat bg-base-300 rounded-lg p-3">
                                    <div className="stat-title text-xs opacity-70">Duration</div>
                                    <div className="stat-value text-lg flex items-center gap-1">
                                        <Clock className="w-4 h-4" />
                                        {convertToDays(course.time)}
                                    </div>
                                    <div className="stat-desc text-xs">days</div>
                                </div>
                            </div>

                            {/* Cost */}
                            <div className="flex items-center gap-2 p-3 rounded-lg bg-base-300">
                                <Coins className="w-4 h-4 text-warning" />
                                <span className="text-sm font-medium">Cost:</span>
                                <span className={`text-sm font-bold ${
                                    canAfford ? 'text-base-content' : 'text-error'
                                }`}>
                                    {formatCurrency(course.cost)}
                                </span>
                            </div>

                            {/* Action */}
                            <div className="card-actions mt-4">
                                {isActive ? (
                                    <div className="w-full">
                                        <div className="badge badge-info badge-soft w-full py-3 gap-2">
                                            <span className="loading loading-spinner loading-xs"></span>
                                            In Progress
                                        </div>
                                    </div>
                                ) : isCompleted ? (
                                    <div className="w-full">
                                        <div className="badge badge-success badge-soft w-full py-3 gap-2">
                                            <img className="h-4 w-4" src={greenTickImg} alt="" />
                                            Completed
                                        </div>
                                    </div>
                                ) : (
                                    <button
                                        className="btn btn-primary btn-block"
                                        disabled={!canEnroll || !canAfford}
                                        onClick={() => handleStartCourse(course.id)}
                                    >
                                        {!canEnroll ? (
                                            <>Already Enrolled</>
                                        ) : !canAfford ? (
                                            <>Insufficient Funds</>
                                        ) : (
                                            <>
                                                <TrendingUp className="w-4 h-4" />
                                                Enroll Now
                                            </>
                                        )}
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );
};
