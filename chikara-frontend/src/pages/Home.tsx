// import equipmentIcon from "@/assets/icons/navitems/equipment.png";
import trainingImg from "@/assets/images/UI/Skills/strength.png";
import inventoryIcon from "@/assets/icons/navitems/inventory.png";
import characterIcon from "@/assets/icons/navitems/character.png";
import abilitiesIcon from "@/assets/icons/navitems/abilities.png";
import talentsIcon from "@/assets/icons/navitems/talents.png";
import dailyTasksIcon from "@/assets/icons/navitems/tasksOld.png";
import blue2BG from "@/assets/images/UI/BackgroundImages/blue3.jpg";
import blueBG from "@/assets/images/UI/BackgroundImages/blueBars.jpg";
import pinkBG from "@/assets/images/UI/BackgroundImages/pink2.jpg";
import greenBG from "@/assets/images/UI/BackgroundImages/green1.jpg";
import orangeBG from "@/assets/images/UI/BackgroundImages/orangeBars.jpg";
import purple2BG from "@/assets/images/UI/BackgroundImages/purple2.jpg";
import { checkLevelGate } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { 
    ChevronRight,
    GraduationCap,
    Inbox,
    Mail,
    Star,
    Lock,
    Sparkles,
    Zap,
    Trophy,
    Target,
    Shield,
    Swords,
    Brain,
    Heart
} from "lucide-react";
import { Link } from "react-router-dom";
import Spinner from "@/components/Spinners/Spinner";
import useGetUnreadNotifications from "@/hooks/api/useGetUnreadNotifications";
import useGetUnreadMessages from "@/hooks/api/useGetUnreadMessages";

function Home() {
    const { data: currentUser, isLoading, isError } = useFetchCurrentUser();
    const { data: unreadNotifications } = useGetUnreadNotifications();
    const { data: unreadMessages } = useGetUnreadMessages();

    const userLevel = currentUser?.level ?? 0;
    const talentsGate = checkLevelGate("talents", userLevel);
    const dailyQuestsGate = checkLevelGate("dailyQuests", userLevel);

    const newsposts = [
        {
            id: 1,
            name: "Welcome to the Chikara Academy Alpha test!",
            href: "/updates",
            username: "The Headmaster",
            userID: 1,
            date: "May 21, 2023",
            datetime: "2023-05-21",
        },
        {
            id: 2,
            name: "Welcome to Alpha 2!",
            href: "/updates",
            username: "The Headmaster",
            userID: 1,
            date: "Feb 01, 2024",
            datetime: "2024-02-01",
        },
    ];

    const mainFeatures = [
        {
            title: "Character",
            subtitle: "View your profile",
            icon: characterIcon,
            background: purple2BG,
            link: "/character",
            locked: false,
            color: "primary",
            iconBg: "bg-primary",
            lucideIcon: Shield,
        },
        {
            title: "Inventory",
            subtitle: "Manage your items",
            icon: inventoryIcon,
            background: blueBG,
            link: "/inventory",
            locked: false,
            color: "info",
            iconBg: "bg-info",
            lucideIcon: Sparkles,
        },
        {
            title: "Training",
            subtitle: "Train your stats",
            icon: trainingImg,
            background: orangeBG,
            link: "/training",
            locked: false,
            color: "warning",
            iconBg: "bg-warning",
            lucideIcon: Swords,
        },
        {
            title: "Talents",
            subtitle: "Unlock new abilities",
            icon: talentsIcon,
            background: greenBG,
            link: "/talents",
            locked: talentsGate.isLocked,
            lockedLevel: talentsGate.requiredLevel,
            points: currentUser?.talentPoints,
            color: "success",
            iconBg: "bg-success",
            lucideIcon: Star,
        },
        {
            title: "Abilities",
            subtitle: "Master your craft",
            icon: abilitiesIcon,
            background: pinkBG,
            link: "/abilities",
            locked: talentsGate.isLocked,
            lockedLevel: talentsGate.requiredLevel,
            color: "secondary",
            iconBg: "bg-secondary",
            lucideIcon: Zap,
        },
        {
            title: "Daily Tasks",
            subtitle: "Complete challenges",
            icon: dailyTasksIcon,
            background: blue2BG,
            link: "/dailies",
            locked: dailyQuestsGate.isLocked,
            lockedLevel: dailyQuestsGate.requiredLevel,
            color: "accent",
            iconBg: "bg-accent",
            lucideIcon: Trophy,
        },
    ];

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <span className="loading loading-ring loading-lg text-primary"></span>
            </div>
        );
    }
    if (isError || !currentUser) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="alert alert-error">
                    <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <span>Error loading user data</span>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen p-4 md:p-6">
            {/* Compact Header with Profile and Stats */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                {/* Profile Card */}
                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body flex-row items-center gap-4 p-4">
                        <div className="avatar">
                            <div className="w-16 mask mask-squircle ring ring-primary ring-offset-base-100 ring-offset-2">
                                <img src={currentUser.avatar || "https://picsum.photos/200"} alt="Avatar" />
                            </div>
                        </div>
                        <div className="flex-1">
                            <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                                {currentUser?.username?.split(" ")[0] || "Student"}
                            </h2>
                            <p className="text-sm text-base-content/70">Level {currentUser?.level || 1} Martial Artist</p>
                            <div className="flex gap-2 mt-2">
                                <div className="badge badge-sm badge-primary">
                                    <Inbox className="w-3 h-3 mr-1" />
                                    {unreadNotifications?.unread || 0}
                                </div>
                                <div className="badge badge-sm badge-secondary">
                                    <Mail className="w-3 h-3 mr-1" />
                                    {unreadMessages?.unread || 0}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-base-content/70">Talent Points</p>
                                <p className="text-3xl font-bold text-secondary">{currentUser?.talentPoints || 0}</p>
                            </div>
                            <Brain className="w-10 h-10 text-secondary opacity-20" />
                        </div>
                    </div>
                </div>

                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-base-content/70">Focus Points</p>
                                <p className="text-3xl font-bold text-accent">{currentUser?.focus || 0}</p>
                            </div>
                            <Target className="w-10 h-10 text-accent opacity-20" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Quick Actions Bar */}
            <div className="flex flex-wrap gap-2 mb-6 justify-center">
                <Link to="/battle" className="btn btn-primary btn-sm">
                    <Swords className="w-4 h-4" />
                    Battle
                </Link>
                <Link to="/quests" className="btn btn-secondary btn-sm">
                    <Trophy className="w-4 h-4" />
                    Quests
                </Link>
                <Link to="/gangs" className="btn btn-accent btn-sm">
                    <Shield className="w-4 h-4" />
                    Gangs
                </Link>
                <Link to="/leaderboard" className="btn btn-info btn-sm">
                    <Star className="w-4 h-4" />
                    Rankings
                </Link>
            </div>

            {/* Main Features Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">

                {mainFeatures.map((feature, index) => (
                    <Link
                        key={index}
                        to={feature.locked ? "#" : feature.link}
                        className={`card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group ${
                            feature.locked ? "opacity-60 cursor-not-allowed" : ""
                        }`}
                        onClick={(e) => feature.locked && e.preventDefault()}
                    >
                        <figure className="relative h-32 overflow-hidden">
                            <img 
                                src={feature.background} 
                                alt={feature.title}
                                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
                            
                            {/* Feature Icon */}
                            <div className="absolute inset-0 flex items-center justify-center">
                                <feature.lucideIcon className="w-8 h-8 text-white opacity-90" />
                            </div>
                            
                            {/* Lock Badge */}
                            {feature.locked && (
                                <div className="absolute top-2 right-2 badge badge-error badge-sm">
                                    <Lock className="w-3 h-3 mr-1" />
                                    {feature.lockedLevel}
                                </div>
                            )}
                            
                            {/* Points Badge */}
                            {feature.points && !feature.locked && (
                                <div className="absolute top-2 left-2 badge badge-warning badge-sm">
                                    {feature.points}
                                </div>
                            )}
                        </figure>
                        
                        <div className="card-body p-3">
                            <h3 className="font-semibold text-sm">{feature.title}</h3>
                            <p className="text-xs text-base-content/60 hidden sm:block">{feature.subtitle}</p>
                        </div>
                    </Link>
                ))}
            </div>

            {/* Two Column Layout for News and Additional Content */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Latest News */}
                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body p-4">
                        <h2 className="card-title text-sm">
                            <GraduationCap className="w-5 h-5" />
                            Latest News
                        </h2>
                        <div className="space-y-2">
                            {newsposts.map((newspost) => (
                                <Link 
                                    key={newspost.id}
                                    to={newspost.href}
                                    className="block p-3 rounded-lg bg-base-200 hover:bg-base-300 transition-colors group"
                                >
                                    <h3 className="font-semibold text-sm group-hover:text-primary transition-colors">
                                        {newspost.name}
                                    </h3>
                                    <p className="text-xs text-base-content/60">
                                        {newspost.username} • {newspost.date}
                                    </p>
                                </Link>
                            ))}
                        </div>
                        <div className="card-actions justify-end mt-2">
                            <Link to="/updates" className="btn btn-ghost btn-sm">View All</Link>
                        </div>
                    </div>
                </div>

                {/* Quick Stats / Activities */}
                <div className="card bg-base-100 shadow-xl">
                    <div className="card-body p-4">
                        <h2 className="card-title text-sm">
                            <Target className="w-5 h-5" />
                            Daily Progress
                        </h2>
                        <div className="space-y-3">
                            <div>
                                <div className="flex justify-between items-center mb-1">
                                    <span className="text-sm">Daily Fatigue</span>
                                    <span className="text-sm font-semibold">20/200</span>
                                </div>
                                <progress className="progress progress-primary w-full" value="20" max="200"></progress>
                            </div>
                            <div>
                                <div className="flex justify-between items-center mb-1">
                                    <span className="text-sm">Quest Progress</span>
                                    <span className="text-sm font-semibold">3/5</span>
                                </div>
                                <progress className="progress progress-success w-full" value="60" max="100"></progress>
                            </div>
                            <div className="divider my-2"></div>
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-base-content/70">Next Energy Refill</span>
                                <span className="text-sm font-mono">00:45:32</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Home;
