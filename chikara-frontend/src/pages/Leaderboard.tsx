import bronzeMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Bronze.png";
import goldMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Gold.png";
import silverMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Silver.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import Spinner from "@/components/Spinners/Spinner";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { formatTimeToNow } from "@/helpers/dateHelpers";
import { api, QueryOptions } from "@/helpers/api";
import { Trophy, Swords, Coins, Shield, Target, Brain, Heart, Zap, User, MapPin, Hammer, Package, ScrollText, TrendingUp, TrendingDown, Dices, Clock, ShoppingCart, Award } from "lucide-react";

interface LeaderboardData {
    level: UserData[];
    pvpwins: UserData[];
    money: UserData[];
    hench: UserData[];
    defensive: UserData[];
    zones: UserData[];
    joblevel: UserData[];
    crafts: UserData[];
    npcwins: UserData[];
    quests: UserData[];
    intelligent: UserData[];
    dexterous: UserData[];
    endurance: UserData[];
    vitality: UserData[];
    muggingGain: UserData[];
    muggingLoss: UserData[];
    casinoWinner: UserData[];
    casinoLoser: UserData[];
    totalMissionHours: UserData[];
    totalStats: UserData[];
    totalBountyRewards: UserData[];
    marketItemsSold: UserData[];
    marketMoneyMade: UserData[];
}

interface LeaderboardResponse {
    data: LeaderboardData;
    lastFetch: number;
}

const useGetLeaderboards = (options: QueryOptions = {}) => {
    return useQuery(
        api.leaderboards.getLeaderBoards.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    ) as { isLoading: boolean; error: any; data: LeaderboardResponse | undefined };
};

function Leaderboard() {
    const leaderboardsConfig = useGameConfig();

    const { isLoading, error, data } = useGetLeaderboards();

    const leaderboardData = data?.data;

    if (leaderboardsConfig?.LEADERBOARDS_DISABLED) {
        return (
            <div className="hero min-h-[50vh]">
                <div className="hero-content text-center">
                    <div className="max-w-md">
                        <h1 className="text-5xl font-bold text-error">Leaderboards Disabled</h1>
                        <p className="py-6 text-base-content/70">The leaderboards are temporarily unavailable. Please check back later!</p>
                        <button className="btn btn-primary" onClick={() => window.location.reload()}>Refresh Page</button>
                    </div>
                </div>
            </div>
        );
    }
    const lastFetch = data?.lastFetch ? data.lastFetch.toString() : null;

    if (error || (!isLoading && !leaderboardData)) {
        return (
            <div className="alert alert-error max-w-lg mx-auto mt-8">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Failed to fetch leaderboards. Please try again later.</span>
            </div>
        );
    }

    // Get icons for different leaderboard types
    const getIcon = (type: string) => {
        switch (type) {
            case "totalStats": return Award;
            case "hench": return Swords;
            case "defensive": return Shield;
            case "dexterous": return Target;
            case "intelligent": return Brain;
            case "endurance": return Zap;
            case "vitality": return Heart;
            case "pvpwins": return Trophy;
            case "level": return TrendingUp;
            case "money": return Coins;
            case "zones": return MapPin;
            case "joblevel": return User;
            case "crafts": return Hammer;
            case "npcwins": return Swords;
            case "quests": return ScrollText;
            case "muggingGain": return TrendingUp;
            case "muggingLoss": return TrendingDown;
            case "casinoWinner": return Dices;
            case "casinoLoser": return Dices;
            case "totalBountyRewards": return Coins;
            case "totalMissionHours": return Clock;
            case "marketItemsSold": return Package;
            case "marketMoneyMade": return ShoppingCart;
            default: return Trophy;
        }
    };

    return (
        <div className="min-h-screen p-4">
            {isLoading ? (
                <div className="flex flex-col items-center justify-center min-h-[50vh] gap-4">
                    <span className="loading loading-spinner loading-lg text-primary"></span>
                    <p className="text-base-content/70">Loading leaderboards...</p>
                </div>
            ) : (
                <>
                    {/* Header with last update time */}
                    {lastFetch && (
                        <div className="flex justify-end mb-4 max-w-7xl mx-auto">
                            <div className="badge badge-soft badge-neutral">
                                <Clock className="w-3 h-3 mr-1" />
                                Updated {formatTimeToNow(lastFetch)} ago
                            </div>
                        </div>
                    )}

                    {/* Leaderboards Grid */}
                    <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4 gap-6 max-w-7xl mx-auto">
                        {/* Stats Leaderboards */}
                        <LeaderOnlyBoard
                            icon={getIcon("totalStats")}
                            hideStats
                            leaderboardData={leaderboardData?.["totalStats"]}
                            attributeName="strength"
                            leaderTitleText="Most Stats"
                            atrributeDisplayName="Total Stats"
                            colorClass="bg-gradient-to-br from-primary to-secondary"
                        />
                        <LeaderOnlyBoard
                            icon={getIcon("hench")}
                            hideStats
                            leaderboardData={leaderboardData?.["hench"]}
                            attributeName="strength"
                            leaderTitleText="Strongest Student"
                            atrributeDisplayName="Strength"
                            colorClass="bg-gradient-to-br from-error to-warning"
                        />
                        <LeaderOnlyBoard
                            icon={getIcon("defensive")}
                            hideStats
                            leaderboardData={leaderboardData?.["defensive"]}
                            attributeName="defence"
                            leaderTitleText="Most Defensive"
                            atrributeDisplayName="Defence"
                            colorClass="bg-gradient-to-br from-info to-primary"
                        />
                        <LeaderOnlyBoard
                            icon={getIcon("dexterous")}
                            hideStats
                            leaderboardData={leaderboardData?.["dexterous"]}
                            attributeName="dexterity"
                            leaderTitleText="Most Dexterous"
                            atrributeDisplayName="Dexterity"
                            colorClass="bg-gradient-to-br from-success to-accent"
                        />
                        <LeaderOnlyBoard
                            icon={getIcon("intelligent")}
                            hideStats
                            leaderboardData={leaderboardData?.["intelligent"]}
                            attributeName="intelligence"
                            leaderTitleText="Most Intelligent"
                            atrributeDisplayName="Intelligence"
                            colorClass="bg-gradient-to-br from-secondary to-accent"
                        />
                        <LeaderOnlyBoard
                            icon={getIcon("endurance")}
                            hideStats
                            leaderboardData={leaderboardData?.["endurance"]}
                            attributeName="endurance"
                            leaderTitleText="Most Endurance"
                            atrributeDisplayName="Endurance"
                            colorClass="bg-gradient-to-br from-warning to-error"
                        />
                        <LeaderOnlyBoard
                            icon={getIcon("vitality")}
                            hideStats
                            leaderboardData={leaderboardData?.["vitality"]}
                            attributeName="vitality"
                            leaderTitleText="Most Vitality"
                            atrributeDisplayName="Vitality"
                            colorClass="bg-gradient-to-br from-accent to-success"
                        />

                        {/* Full Leaderboards */}
                        <IndividualLeaderboard
                            icon={getIcon("pvpwins")}
                            leaderboardData={leaderboardData?.["pvpwins"]}
                            leaderTitleText="Most Feared Fighter"
                            attributeName="battleWins"
                            atrributeDisplayName="PvP Wins"
                            hideStats={false}
                            colorClass="from-error to-warning"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("level")}
                            leaderboardData={leaderboardData?.["level"]}
                            leaderTitleText="Biggest Grinder"
                            attributeName="level"
                            atrributeDisplayName="Level"
                            hideStats={false}
                            colorClass="from-primary to-secondary"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("money")}
                            hideStats
                            leaderboardData={leaderboardData?.["money"]}
                            leaderTitleText="Richest Oligarch"
                            attributeName="bank_balance"
                            atrributeDisplayName="Money"
                            colorClass="from-warning to-accent"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("zones")}
                            leaderboardData={leaderboardData?.["zones"]}
                            attributeName="roguelikeMapsCompleted"
                            leaderTitleText="Highest Zone Reached"
                            atrributeDisplayName="Zone"
                            hideStats={false}
                            colorClass="from-secondary to-primary"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("joblevel")}
                            hideStats
                            leaderboardData={leaderboardData?.["joblevel"]}
                            attributeName="joblevel"
                            leaderTitleText="Highest Job Level"
                            atrributeDisplayName="Job Level"
                            colorClass="from-info to-success"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("crafts")}
                            leaderboardData={leaderboardData?.["crafts"]}
                            attributeName="craftsCompleted"
                            leaderTitleText="Master Crafter"
                            atrributeDisplayName="Items Crafted"
                            hideStats={false}
                            colorClass="from-accent to-warning"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("npcwins")}
                            leaderboardData={leaderboardData?.["npcwins"]}
                            attributeName="npcBattleWins"
                            leaderTitleText="Monster Hunter"
                            atrributeDisplayName="NPC Wins"
                            hideStats={false}
                            colorClass="from-error to-primary"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("quests")}
                            leaderboardData={leaderboardData?.["quests"]}
                            attributeName="questsCompleted"
                            leaderTitleText="Quest Master"
                            atrributeDisplayName="Tasks Complete"
                            hideStats={false}
                            colorClass="from-success to-info"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("muggingGain")}
                            leaderboardData={leaderboardData?.["muggingGain"]}
                            attributeName="totalMuggingGain"
                            leaderTitleText="Top Mugger"
                            atrributeDisplayName="Gained from mugging"
                            colorClass="from-warning to-error"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("muggingLoss")}
                            isNegative
                            leaderboardData={leaderboardData?.["muggingLoss"]}
                            attributeName="totalMuggingLoss"
                            leaderTitleText="Most Mugged"
                            atrributeDisplayName="Lost from mugging"
                            colorClass="from-error to-error/70"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("casinoWinner")}
                            leaderboardData={leaderboardData?.["casinoWinner"]}
                            attributeName="totalCasinoProfitLoss"
                            leaderTitleText="Lucky Gambler"
                            atrributeDisplayName="Casino Profit"
                            colorClass="from-accent to-success"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("casinoLoser")}
                            isRed
                            leaderboardData={leaderboardData?.["casinoLoser"]}
                            attributeName="totalCasinoProfitLoss"
                            leaderTitleText="Unlucky Gambler"
                            atrributeDisplayName="Casino Loss"
                            colorClass="from-error to-error/70"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("totalBountyRewards")}
                            leaderboardData={leaderboardData?.["totalBountyRewards"]}
                            attributeName="totalBountyRewards"
                            leaderTitleText="Bounty Hunter"
                            atrributeDisplayName="Bounty Claimed"
                            colorClass="from-primary to-accent"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("totalMissionHours")}
                            leaderboardData={leaderboardData?.["totalMissionHours"]}
                            attributeName="totalMissionHours"
                            leaderTitleText="Mission Expert"
                            atrributeDisplayName="Mission Hours"
                            colorClass="from-info to-secondary"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("marketItemsSold")}
                            leaderboardData={leaderboardData?.["marketItemsSold"]}
                            attributeName="marketItemsSold"
                            leaderTitleText="Market Tycoon"
                            atrributeDisplayName="Items Sold"
                            colorClass="from-success to-accent"
                        />
                        <IndividualLeaderboard
                            icon={getIcon("marketMoneyMade")}
                            leaderboardData={leaderboardData?.["marketMoneyMade"]}
                            attributeName="marketMoneyMade"
                            leaderTitleText="Trading Mogul"
                            atrributeDisplayName="Yen Made"
                            colorClass="from-warning to-success"
                        />
                    </div>
                </>
            )}
        </div>
    );
}

interface UserData {
    id: number;
    username: string;
    avatar: string | null;
    level?: number;
    bank_balance?: number;
    strength?: number;
    defence?: number;
    dexterity?: number;
    intelligence?: number;
    endurance?: number;
    vitality?: number;
    joblevel?: number;
    user_achievements?: {
        battleWins?: number | null;
        roguelikeMapsCompleted?: number | null;
        craftsCompleted?: number | null;
        npcBattleWins?: number | null;
        questsCompleted?: number | null;
        totalMuggingGain?: number | null;
        totalMuggingLoss?: number | null;
        totalCasinoProfitLoss?: number | null;
        totalMissionHours?: number | null;
        totalBountyRewards?: number | null;
        marketItemsSold?: number | null;
        marketMoneyMade?: number | null;
    } | null;
    [key: string]: unknown;
}

const getAttribute = (user: UserData, attributeName: string): number => {
    // Handle achievement-based attributes
    if (user.user_achievements && attributeName in user.user_achievements) {
        const value = user.user_achievements[attributeName as keyof typeof user.user_achievements];
        return typeof value === "number" ? value : 0;
    }

    // Handle direct user attributes
    const value = user[attributeName];
    return typeof value === "number" ? value : 0;
};

function IndividualLeaderboard(props: {
    leaderboardData: UserData[] | undefined;
    attributeName: string;
    atrributeDisplayName: string;
    leaderTitleText: string;
    hideStats?: boolean;
    isNegative?: boolean;
    isRed?: boolean;
    icon?: React.ElementType;
    colorClass?: string;
}) {
    const { USERS_PER_BOARD } = useGameConfig();
    if (!props?.leaderboardData?.length) return null;
    if (Object.keys(props?.leaderboardData)?.length < 1) return;
    const leaderboardData = props.leaderboardData.slice(1);
    const attributeName = props.attributeName;
    const attributeDisplayName = props.atrributeDisplayName;
    const leaderTitleText = props.leaderTitleText;
    const hideStats = props.hideStats;
    const isNegative = props.isNegative;
    const isRed = props.isRed;
    const Icon = props.icon || Trophy;
    const colorClass = props.colorClass || "from-primary to-secondary";

    const leader = props.leaderboardData[0];

    return (
        <div className="card card-xl row-span-2 bg-base-200 shadow-xl overflow-hidden group hover:shadow-2xl transition-all duration-300">
            {/* Header with gradient */}
            <div className={`bg-gradient-to-br ${colorClass} p-4 text-white relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                            <Icon className="w-6 h-6" />
                        </div>
                        <h3 className="text-xl font-bold">{leaderTitleText}</h3>
                    </div>
                    <div className="badge badge-lg badge-soft bg-white/20 text-white border-0">
                        Top {USERS_PER_BOARD + 1}
                    </div>
                </div>
            </div>

            {/* Champion Section */}
            <div className="p-6 bg-base-100 border-b border-base-300">
                <div className="flex items-center gap-4">
                    <div className="relative">
                        <img className="absolute -top-2 -left-2 h-12 w-12 z-10" src={goldMedal} alt="Gold medal" />
                        <Link to={`/profile/${leader.id}`} className="block">
                            <div className="avatar">
                                <div className="w-20 rounded-xl ring ring-warning ring-offset-base-100 ring-offset-2">
                                    <DisplayAvatar src={leader} />
                                </div>
                            </div>
                        </Link>
                    </div>
                    <div className="flex-1">
                        <Link to={`/profile/${leader.id}`} className="hover:underline">
                            <p className="text-xl font-bold text-primary">{leader.username}</p>
                        </Link>
                        <div className="flex items-center gap-2 mt-1">
                            <span className="text-base-content/70">{attributeDisplayName}:</span>
                            <span className={cn(
                                "font-bold text-lg",
                                isNegative || isRed ? "text-error" : "text-success"
                            )}>
                                {isNegative ? "-" : ""}
                                {hideStats ? "???" : getAttribute(leader, attributeName).toLocaleString()}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Runners Up */}
            <div className="overflow-x-auto">
                <table className="table table-zebra">
                    <tbody>
                        {leaderboardData.slice(0, USERS_PER_BOARD).map((person, i) => (
                            <tr key={person.id} className="hover">
                                <td className="w-16">
                                    <div className="flex justify-center">
                                        {i === 0 && (
                                            <img className="h-8 w-8" src={silverMedal} alt="Silver medal" />
                                        )}
                                        {i === 1 && (
                                            <img className="h-8 w-8" src={bronzeMedal} alt="Bronze medal" />
                                        )}
                                        {i > 1 && (
                                            <div className="badge badge-lg badge-ghost font-bold">
                                                {i + 2}
                                            </div>
                                        )}
                                    </div>
                                </td>
                                <td>
                                    <div className="flex items-center gap-3">
                                        <Link to={`/profile/${person.id}`}>
                                            <div className="avatar">
                                                <div className="w-10 rounded-lg">
                                                    <DisplayAvatar src={person} />
                                                </div>
                                            </div>
                                        </Link>
                                        <div>
                                            <Link to={`/profile/${person.id}`} className="hover:underline">
                                                <div className="font-semibold">{person.username}</div>
                                            </Link>
                                            <div className="text-sm text-base-content/50">Student #{person.id}</div>
                                        </div>
                                    </div>
                                </td>
                                <td className="text-right">
                                    <div className={cn(
                                        "font-bold",
                                        isNegative || isRed ? "text-error" : "text-success"
                                    )}>
                                        {isNegative ? "-" : ""}
                                        {hideStats ? "" : getAttribute(person, attributeName).toLocaleString()}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}

function LeaderOnlyBoard(props: {
    leaderboardData: UserData[] | undefined;
    attributeName: string;
    atrributeDisplayName: string;
    leaderTitleText: string;
    hideStats?: boolean;
    icon?: React.ElementType;
    colorClass?: string;
}) {
    if (!props?.leaderboardData?.length) return null;
    const attributeName = props.attributeName;
    const attributeDisplayName = props.atrributeDisplayName;
    const leaderTitleText = props.leaderTitleText;
    const hideStats = props.hideStats;
    const Icon = props.icon || Trophy;
    const colorClass = props.colorClass || "bg-gradient-to-br from-primary to-secondary";

    const leader = props.leaderboardData[0];

    return (
        <div className="card bg-base-200 shadow-xl overflow-hidden group hover:shadow-2xl transition-all duration-300">
            {/* Header with gradient */}
            <div className={`${colorClass} p-4 text-white relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative flex items-center gap-3">
                    <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                        <Icon className="w-5 h-5" />
                    </div>
                    <h3 className="text-lg font-bold flex-1">{leaderTitleText}</h3>
                    <div className="badge badge-soft bg-white/20 text-white border-0">
                        #1
                    </div>
                </div>
            </div>

            {/* Champion Section */}
            <div className="p-6 flex items-center gap-4">
                <div className="relative">
                    <img className="absolute -top-2 -left-2 h-10 w-10 z-10" src={goldMedal} alt="Gold medal" />
                    <Link to={`/profile/${leader.id}`} className="block">
                        <div className="avatar">
                            <div className="w-16 rounded-xl ring ring-warning ring-offset-base-100 ring-offset-2">
                                <DisplayAvatar src={leader} />
                            </div>
                        </div>
                    </Link>
                </div>
                <div className="flex-1">
                    <Link to={`/profile/${leader.id}`} className="hover:underline">
                        <p className="text-lg font-bold text-primary">{leader.username}</p>
                    </Link>
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-base-content/70">{attributeDisplayName}:</span>
                        <span className="font-bold text-success">
                            {hideStats ? "???" : getAttribute(leader, attributeName).toLocaleString()}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Leaderboard;
