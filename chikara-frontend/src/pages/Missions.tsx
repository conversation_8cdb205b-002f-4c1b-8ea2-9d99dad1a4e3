import { DisplayItem } from "@/components/DisplayItem";
import { getNextMidnightDate, formatTimeToNow } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { useMissionList, type Mission } from "@/features/missions/api/useMissionList";
import { useCurrentMission } from "@/features/missions/api/useCurrentMission";
import { useStartMission } from "@/features/missions/api/useStartMission";
import { useCancelMission } from "@/features/missions/api/useCancelMission";
import { useState } from "react";
import { CountdownTimer } from "../components/Layout/CountdownTimer";
import type { User } from "@/types/user";
import { formatCurrency } from "@/utils/currencyHelpers";
import { Clock, Award, AlertTriangle, Coins, Target, Timer, ChevronRight, Lock } from "lucide-react";

interface Tab {
    id: number;
    name: string;
    current: boolean;
}

const convertToHours = (milliseconds: number): number => {
    const hours = Math.floor(milliseconds / (60 * 60 * 1000));
    return hours;
};

function Missions() {
    const { isLoading, data } = useMissionList();
    const { data: currentUser } = useFetchCurrentUser();
    const { data: currentMission } = useCurrentMission(!!currentUser?.currentMission);
    const [selectedTier, setSelectedTier] = useState<number>(1);

    const { MISSION_TIER_REQ_LEVELS, MISSION_TIER_REQ_HOURS } = useGameConfig();

    const startMission = useStartMission();
    const cancelMission = useCancelMission();

    const handleStartMission = (missionId: number): void => {
        startMission.mutate({ id: missionId });
    };

    const handleCancelMission = (): void => {
        cancelMission.mutate({});
    };

    const getProgressBarValue = (reqHours: number, totalHours: number): number => {
        const percentage = (totalHours / reqHours) * 100;
        return percentage;
    };

    const currentTierReqHours = MISSION_TIER_REQ_HOURS[selectedTier - 1];
    const currentTierReqLevel = MISSION_TIER_REQ_LEVELS[selectedTier - 1];

    const selectedMissions = data?.filter((m: Mission) => m.tier === selectedTier) || [];

    const midnightDate = getNextMidnightDate();

    const totalMissionHours = currentUser?.user_achievements?.totalMissionHours || 0;

    if (isLoading) return null;

    return (
        <div className="min-h-screen text-white">
            {/* Header with Alert */}
            <div className="border-b border-base-300 bg-gradient-to-b from-base-100 to-base-200">
                <div className="max-w-7xl mx-auto px-4 py-4">
                    {/* Alert */}
                    <div role="alert" className="alert alert-warning alert-soft mb-4">
                        <AlertTriangle className="w-5 h-5" />
                        <span className="text-sm">You cannot access any other gameplay activities whilst you're on a mission.</span>
                    </div>

                    {/* Timer */}
                    <div className="flex justify-end items-center gap-2 text-sm">
                        <span className="text-base-content/60">New missions in:</span>
                        <div className="badge badge-accent badge-lg gap-1">
                            <Clock className="w-4 h-4" />
                            <CountdownTimer showHours targetDate={midnightDate} showSeconds={false} />
                        </div>
                    </div>
                </div>
            </div>

            {/* Current Mission Card */}
            {currentUser?.currentMission && currentMission ? (
                <div className="max-w-7xl mx-auto px-4 py-6">
                    <div className="card card-border bg-base-200 border-primary shadow-xl">
                        <div className="card-body">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                                {/* Mission Name */}
                                <div className="text-center md:text-left">
                                    <p className="text-xs uppercase text-base-content/60 mb-1">Current Mission</p>
                                    <h2 className="text-xl font-bold text-accent">{currentMission?.missionName}</h2>
                                </div>

                                {/* Rewards */}
                                <div className="text-center md:col-span-2">
                                    <p className="text-xs uppercase text-base-content/60 mb-2">Rewards</p>
                                    <div className="flex flex-wrap justify-center gap-3">
                                        {currentMission.minCashReward > 0 && (
                                            <div className="badge badge-success badge-lg gap-1">
                                                <Coins className="w-4 h-4" />
                                                {formatCurrency(currentMission.minCashReward)} ~ {formatCurrency(currentMission.maxCashReward)}
                                            </div>
                                        )}
                                        {currentMission.minExpReward > 0 && (
                                            <div className="badge badge-info badge-lg gap-1">
                                                <Target className="w-4 h-4" />
                                                {currentMission.minExpReward} ~ {currentMission.maxExpReward} EXP
                                            </div>
                                        )}
                                        {currentMission.item && (
                                            <div className="badge badge-secondary badge-lg gap-1">
                                                <DisplayItem item={currentMission.item} height="h-5 w-5" />
                                                <span>x{currentMission.itemRewardQuantity}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Time & Action */}
                                <div className="text-center md:text-right">
                                    <p className="text-xs uppercase text-base-content/60 mb-1">Finishes in</p>
                                    <p className="text-lg font-bold text-success mb-2">{formatTimeToNow(currentUser?.missionEnds)}</p>
                                    <button 
                                        className="btn btn-error btn-sm"
                                        onClick={() => handleCancelMission()}
                                    >
                                        Cancel Mission
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ) : null}

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 pb-8">
                {/* Tier Tabs */}
                <TierTabs
                    selectedTier={selectedTier}
                    setSelectedTier={setSelectedTier}
                    currentLevel={currentUser?.level}
                    missionTierLevelReqs={MISSION_TIER_REQ_LEVELS}
                />

                {/* Requirements or Mission List */}
                {totalMissionHours < currentTierReqHours || (currentUser?.level ?? 0) < currentTierReqLevel ? (
                    <div className="card bg-base-200 mt-6">
                        <div className="card-body text-center">
                            {(currentUser?.level ?? 0) < currentTierReqLevel && (
                                <div className="alert alert-error alert-soft mb-4">
                                    <Lock className="w-5 h-5" />
                                    <span>Requires level {currentTierReqLevel}</span>
                                </div>
                            )}

                            {totalMissionHours < currentTierReqHours && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold">Mission Experience Required</h3>
                                    <p className="text-base-content/80">
                                        Complete more missions to unlock this tier
                                    </p>
                                    <div className="max-w-md mx-auto w-full">
                                        <div className="flex justify-between text-sm mb-2">
                                            <span>Progress</span>
                                            <span className="text-primary">{totalMissionHours} / {currentTierReqHours} hours</span>
                                        </div>
                                        <progress 
                                            className="progress progress-primary w-full" 
                                            value={getProgressBarValue(currentTierReqHours, totalMissionHours)} 
                                            max="100"
                                        ></progress>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    <MissionsTable
                        missions={selectedMissions}
                        handleStartMission={handleStartMission}
                        currentUser={currentUser}
                    />
                )}
            </div>
        </div>
    );
}

export default Missions;

interface MissionsTableProps {
    missions: Mission[] | undefined;
    handleStartMission: (missionId: number) => void;
    currentUser: User | undefined;
}

const MissionsTable = ({ missions, handleStartMission, currentUser }: MissionsTableProps) => {
    return (
        <div className="mt-6 space-y-4">
            {missions?.map((mission: Mission) => (
                <div key={mission.id} className="card card-border bg-base-200 hover:bg-base-300 transition-colors">
                    <div className="card-body p-4 md:p-6">
                        <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                            {/* Mission Info */}
                            <div className="md:col-span-6 space-y-2">
                                <h3 className="text-lg font-bold text-accent">{mission.missionName}</h3>
                                <p className="text-sm text-base-content/80">{mission.description}</p>
                                <div className="flex items-center gap-2 text-sm text-base-content/60">
                                    <Timer className="w-4 h-4" />
                                    <span>{convertToHours(mission.duration)} hours</span>
                                </div>
                            </div>

                            {/* Rewards */}
                            <div className="md:col-span-4">
                                <p className="text-xs uppercase text-base-content/60 mb-2">Rewards</p>
                                <div className="flex flex-wrap gap-2">
                                    {mission.minCashReward > 0 && (
                                        <div className="badge badge-success gap-1">
                                            <Coins className="w-3 h-3" />
                                            {formatCurrency(mission.minCashReward)} ~ {formatCurrency(mission.maxCashReward)}
                                        </div>
                                    )}
                                    {mission.minExpReward > 0 && (
                                        <div className="badge badge-info gap-1">
                                            <Target className="w-3 h-3" />
                                            {mission.minExpReward} ~ {mission.maxExpReward} EXP
                                        </div>
                                    )}
                                    {mission.itemReward && (
                                        <div className="badge badge-secondary gap-1">
                                            <DisplayItem item={mission.itemReward} height="h-4 w-4" />
                                            <span>x{mission.itemRewardQuantity}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Action */}
                            <div className="md:col-span-2 flex justify-end">
                                <button
                                    className="btn btn-primary btn-sm gap-2"
                                    disabled={
                                        currentUser
                                            ? currentUser.level < mission.levelReq ||
                                              Boolean(currentUser.missionEnds && currentUser.missionEnds > 0)
                                            : true
                                    }
                                    onClick={() => handleStartMission(mission.id)}
                                >
                                    {currentUser && currentUser.level < mission.levelReq ? (
                                        <>
                                            <Lock className="w-4 h-4" />
                                            <span className="hidden sm:inline">Locked</span>
                                        </>
                                    ) : (
                                        <>
                                            <ChevronRight className="w-4 h-4" />
                                            <span>Begin</span>
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
};

interface TierTabsProps {
    selectedTier: number;
    setSelectedTier: (tier: number) => void;
    currentLevel: number | undefined;
    missionTierLevelReqs: readonly number[];
}

const TierTabs = ({ selectedTier, setSelectedTier, currentLevel, missionTierLevelReqs }: TierTabsProps) => {
    const tabs = [
        { id: 1, name: "Tier I" },
        { id: 2, name: "Tier II" },
        { id: 3, name: "Tier III" },
        { id: 4, name: "Tier IV" },
        { id: 5, name: "Tier V" },
    ];

    return (
        <div role="tablist" className="tabs tabs-box tabs-lg mt-6">
            {tabs.map((tab) => {
                const isLocked = missionTierLevelReqs[tab.id - 1] > (currentLevel || 0);
                const isActive = selectedTier === tab.id;
                
                return (
                    <button
                        key={tab.id}
                        role="tab"
                        className={`tab ${
                            isActive ? "tab-active" : ""
                        } ${isLocked ? "tab-disabled" : ""} flex-col gap-1`}
                        onClick={() => !isLocked && setSelectedTier(tab.id)}
                        disabled={isLocked}
                    >
                        <span className="font-semibold">{tab.name}</span>
                        {isLocked && (
                            <span className="text-xs text-error flex items-center gap-1">
                                <Lock className="w-3 h-3" />
                                Level {missionTierLevelReqs[tab.id - 1]}
                            </span>
                        )}
                    </button>
                );
            })}
        </div>
    );
};
