import { PropertyList } from "@/features/property/components/PropertyList";
import { UserPropertyList } from "@/features/property/components/UserPropertyList";
import { Home, ShoppingBag, Sparkles } from "lucide-react";

export default function PropertyPage() {
    return (
        <div className="min-h-screen">
            {/* Hero Section */}
            <div className="hero bg-gradient-to-br from-primary/20 via-secondary/10 to-accent/20 py-8 mb-8">
                <div className="hero-content text-center">
                    <div className="max-w-2xl">
                        <h1 className="text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                            Property Empire
                        </h1>
                        <p className="py-4 text-lg text-base-content/80">
                            Build your real estate portfolio and unlock powerful buffs!
                        </p>
                        <div className="flex flex-wrap gap-3 justify-center">
                            <div className="badge badge-lg badge-primary gap-2">
                                <Home className="w-4 h-4" />
                                Passive Income
                            </div>
                            <div className="badge badge-lg badge-secondary gap-2">
                                <Sparkles className="w-4 h-4" />
                                Combat Buffs
                            </div>
                            <div className="badge badge-lg badge-accent gap-2">
                                <ShoppingBag className="w-4 h-4" />
                                Exclusive Perks
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-7xl mx-auto px-4 space-y-8">
                {/* User's Properties */}
                <div className="card bg-base-200 shadow-xl">
                    <div className="card-body">
                        <UserPropertyList />
                    </div>
                </div>

                {/* Available Properties to Purchase */}
                <div className="card bg-base-200 shadow-xl">
                    <div className="card-body">
                        <PropertyList />
                    </div>
                </div>
            </div>
        </div>
    );
}
