import DiscordIcon from "@/assets/icons/logos/DiscordIcon";
import EmailSettings from "@/features/settings/components/EmailSettings";
import InterfaceSettings from "@/features/settings/components/InterfaceSettings";
import NotificationSettings from "@/features/settings/components/NotificationSettings";
import PasswordSettings from "@/features/settings/components/PasswordSettings";
import ProfileSettings from "@/features/settings/components/ProfileSettings";
import { handleLogout } from "@/helpers/handleLogout";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { User } from "@/types/user";
import { AtSign, Bell, CircleUser, Key, LayoutGrid, LogOut, Sparkles, Users } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

type SettingsPage = "Profile" | "Email" | "Password" | "Interface" | "Notifications";

interface NavigationItem {
    name: SettingsPage;
    current: boolean;
    icon: React.ComponentType<{ className?: string }>;
}

export default function Settings() {
    const { data: currentUser } = useFetchCurrentUser();
    const [selectedPage, setSelectedPage] = useState<SettingsPage>("Profile");

    const confirmLogout = (): void => {
        window.confirm("Are you sure you want to logout?") && handleLogout();
    };

    const currentTab = (tabname: SettingsPage): boolean => {
        return selectedPage === tabname;
    };

    const subNavigation: NavigationItem[] = [
        { name: "Profile", current: currentTab("Profile"), icon: CircleUser },
        { name: "Email", current: currentTab("Email"), icon: AtSign },
        { name: "Password", current: currentTab("Password"), icon: Key },
        {
            name: "Interface",
            current: currentTab("Interface"),
            icon: LayoutGrid,
        },
        {
            name: "Notifications",
            current: currentTab("Notifications"),
            icon: Bell,
        },
    ];

    const renderPage = (): React.ReactNode => {
        switch (selectedPage) {
            case "Profile":
                return <ProfileSettings currentUser={currentUser} />;
            case "Email":
                return <EmailSettings currentUser={currentUser} />;
            case "Password":
                return <PasswordSettings currentUser={currentUser} />;
            case "Interface":
                return <InterfaceSettings currentUser={currentUser} />;
            case "Notifications":
                return <NotificationSettings currentUser={currentUser} />;
            default:
                return <ProfileSettings currentUser={currentUser} />;
        }
    };

    return (
        <div className="mx-auto max-w-7xl px-4 py-8">
            <div className="card bg-base-200 shadow-2xl">
                <div className="card-body p-0">
                    <div className="flex flex-col lg:flex-row">
                        {/* Sidebar Navigation */}
                        <aside className="w-full lg:w-80 bg-base-300 p-6">
                            <div className="mb-6">
                                <h2 className="text-2xl font-bold font-display text-primary flex items-center gap-2">
                                    <Sparkles className="size-6" />
                                    Settings
                                </h2>
                                <p className="text-sm text-base-content/70 mt-1">Manage your account</p>
                            </div>
                            
                            <nav className="menu menu-lg p-0">
                                {subNavigation.map((item) => (
                                    <li key={item.name} className="mb-2">
                                        <button
                                            aria-current={item.current ? "page" : undefined}
                                            className={
                                                item.current
                                                    ? "btn btn-primary btn-soft"
                                                    : "btn btn-ghost hover:btn-primary hover:btn-soft"
                                            }
                                            onClick={() => setSelectedPage(item.name)}
                                        >
                                            <item.icon className="size-5" />
                                            <span className="flex-1 text-left">{item.name}</span>
                                        </button>
                                    </li>
                                ))}
                                
                                <div className="divider my-4"></div>
                                
                                <li>
                                    <button
                                        className="btn btn-error btn-outline hover:btn-error"
                                        onClick={confirmLogout}
                                    >
                                        <LogOut className="size-5" />
                                        <span className="flex-1 text-left">Sign Out</span>
                                    </button>
                                </li>
                            </nav>
                        </aside>

                        {/* Main Content Area */}
                        <div className="flex-1 p-6 lg:p-8">
                            {/* Account Actions */}
                            <div className="mb-8">
                                <div className="card bg-gradient-to-br from-primary/10 to-secondary/10 border-2 border-primary/20">
                                    <div className="card-body">
                                        <h3 className="card-title text-primary mb-2">
                                            <Sparkles className="size-5" />
                                            Quick Actions
                                        </h3>
                                        <p className="text-sm text-base-content/70 mb-4">
                                            Enhance your account with these features
                                        </p>
                                        
                                        <div className="flex flex-wrap gap-3">
                                            {!currentUser?.discordID && (
                                                <Link to="/discord">
                                                    <button className="btn btn-secondary btn-sm gap-2">
                                                        <DiscordIcon className="size-4" />
                                                        Link Discord
                                                    </button>
                                                </Link>
                                            )}
                                            
                                            {currentUser?.level > 4 && (
                                                <Link to="/refer">
                                                    <button className="btn btn-accent btn-sm gap-2">
                                                        <Users className="size-4" />
                                                        Refer Friends
                                                    </button>
                                                </Link>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            {/* Dynamic Content */}
                            <div className="animate-fade-in">
                                {renderPage()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
