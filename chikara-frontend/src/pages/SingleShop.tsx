import { getNextSundayMidnight } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { Heart, ShoppingBag, Package, Clock, AlertCircle } from "lucide-react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { CountdownTimer } from "../components/Layout/CountdownTimer";
import { ShopBuyItems } from "../features/shop/components/ShopBuyItems";
import { ShopSellItems } from "../features/shop/components/ShopSellItems";
import SingleShopkeeper from "../features/shop/components/SingleShopkeeper";
import Spinner from "@/components/Spinners/Spinner";

// Types
type SellOrBuyTab = "Buy" | "Sell";

interface Tab {
    heart: boolean;
    current: boolean;
    amount: number;
    disabled: boolean;
}

interface BuyOrSellTab {
    name: SellOrBuyTab;
    current: boolean;
}

function classNames(...classes: (string | boolean | undefined)[]): string {
    return classes.filter(Boolean).join(" ");
}

export default function SingleShop() {
    const { shopID } = useParams<{ shopID: string }>();
    const [sellOrBuyTab, setSellOrBuyTab] = useState<SellOrBuyTab>("Buy");
    const [currentHeartTab, setCurrentHeartTab] = useState<number>(0);
    const { LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT } = useGameConfig();

    const { data: traderRep } = useQuery(
        api.shop.getTraderRep.queryOptions({
            input: { shopId: Number.parseInt(shopID || "0") },
            enabled: !!shopID && !isNaN(Number.parseInt(shopID)) && Number.parseInt(shopID) > 0,
        })
    );
    const { data: shopInfo, isLoading } = useQuery(
        api.shop.shopInfo.queryOptions({
            input: { shopId: Number.parseInt(shopID || "0") },
            enabled: !!shopID && !isNaN(Number.parseInt(shopID)) && Number.parseInt(shopID) > 0,
        })
    );
    const { data: currentUser } = useFetchCurrentUser();
    const repLevel = traderRep?.reputationLevel || 0;

    const allTabFilter = shopInfo?.shop_listing?.filter((listing) => listing?.repRequired <= repLevel);

    const filteredListings = shopInfo?.shop_listing.filter((listing) => listing?.repRequired === currentHeartTab);
    const shopData = {
        ...shopInfo,
        shop_listing: currentHeartTab === 0 ? allTabFilter : filteredListings,
    };

    const tabs: Tab[] = [
        {
            heart: false,
            current: currentHeartTab === 0,
            amount: 0,
            disabled: false,
        },
        {
            heart: true,
            current: currentHeartTab === 1,
            amount: 1,
            disabled: !repLevel || repLevel < 1 || shopInfo?.id === 6,
        },
        {
            heart: true,
            current: currentHeartTab === 2,
            amount: 2,
            disabled: !repLevel || repLevel < 2 || shopInfo?.id === 6,
        },
        {
            heart: true,
            current: currentHeartTab === 3,
            amount: 3,
            disabled: !repLevel || repLevel < 3 || shopInfo?.id === 6,
        },
        {
            heart: true,
            current: currentHeartTab === 4,
            amount: 4,
            disabled: true,
        },
    ];

    const buyOrSellTabs: BuyOrSellTab[] = [
        { name: "Buy", current: sellOrBuyTab === "Buy" },
        { name: "Sell", current: sellOrBuyTab === "Sell" },
    ];

    function isSundayShopOpen(): boolean {
        if (shopData.id === 6 && shopData.disabled) return false;
        return true;
    }

    if (isLoading) return <Spinner center />;
    if (shopInfo?.id === 6 && !isSundayShopOpen() && currentUser?.userType !== "admin")
        return (
            <div className="min-h-[50vh] flex items-center justify-center bg-[#0d1b2a]">
                <div className="p-6 rounded-lg border border-yellow-500/50 bg-yellow-500/10 max-w-sm text-center">
                    <div className="flex items-center justify-center gap-2 text-yellow-400">
                        <AlertCircle className="h-6 w-6" />
                        <span className="text-lg">Shop is closed until 12:00 Sunday</span>
                    </div>
                </div>
            </div>
        );
    return (
        <main className="min-h-screen bg-[#0d1b2a] text-white">
            <div className="mx-auto max-w-7xl px-4 py-6">
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <SingleShopkeeper
                        singleShop={shopData}
                        setSellOrBuyTab={setSellOrBuyTab}
                        sellOrBuyTab={sellOrBuyTab}
                        cash={currentUser?.cash.toString()}
                    />
                    <div className="col-span-2">
                        <div className="rounded-xl bg-[#162639] border border-[#2a4a7c] shadow-xl">
                            {/* BUY OR SELL TABS MOBILE */}
                            <div className="block sm:hidden">
                                <div className="flex border-b border-[#2a4a7c]">
                                    {buyOrSellTabs.map((tab) => (
                                        <button
                                            key={tab.name}
                                            className={cn(
                                                "flex-1 px-4 py-3 text-center font-medium transition-all duration-200",
                                                tab.current
                                                    ? "text-white border-b-2 border-blue-500 bg-[#1a2f4a]"
                                                    : "text-gray-400 hover:text-gray-300 hover:bg-[#1a2f4a]/50"
                                            )}
                                            data-testid={
                                                tab.name === "Buy"
                                                    ? "buy-item-button-mobile"
                                                    : "sell-item-button-mobile"
                                            }
                                            onClick={() => setSellOrBuyTab(tab.name)}
                                        >
                                            <div className="flex items-center justify-center">
                                                {tab.name === "Buy" ? (
                                                    <ShoppingBag className="mr-2 h-5 w-5" />
                                                ) : (
                                                    <Package className="mr-2 h-5 w-5" />
                                                )}
                                                {tab.name}
                                            </div>
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* HEART TABS */}
                            <div className="p-6">
                                {/* HEART TABS MOBILE */}
                                <div className={cn("md:hidden", sellOrBuyTab === "Buy" ? "block" : "hidden")}>
                                    <div className="flex gap-2">
                                        {tabs.map((tab) => (
                                            <button
                                                key={tab.amount}
                                                className={cn(
                                                    "flex-1 py-2 px-3 rounded-lg font-medium transition-all duration-200 border",
                                                    tab.current
                                                        ? "bg-blue-600 text-white border-blue-500"
                                                        : tab.disabled
                                                        ? "bg-[#0d1b2a]/50 text-gray-500 border-[#2a4a7c] cursor-not-allowed"
                                                        : "bg-[#1a2f4a] text-gray-300 border-[#2a4a7c] hover:bg-[#1a2f4a]/80 hover:text-white"
                                                )}
                                                onClick={() => {
                                                    if (!tab.disabled) {
                                                        setCurrentHeartTab(tab.amount);
                                                    }
                                                }}
                                            >
                                                {tab.heart ? (
                                                    <div className="flex items-center justify-center gap-1">
                                                        <span className="text-lg">{tab.amount}</span>
                                                        <Heart
                                                            className={cn(
                                                                "h-5 w-5",
                                                                tab.disabled
                                                                    ? "text-gray-500"
                                                                    : "text-pink-500 fill-pink-500"
                                                            )}
                                                        />
                                                    </div>
                                                ) : (
                                                    <span className="text-lg">All</span>
                                                )}
                                            </button>
                                        ))}
                                    </div>
                                </div>

                                {/* HEART TABS DESKTOP */}
                                <div className="hidden md:block mb-4">
                                    <div className="flex gap-2">
                                        {tabs.map((tab) => (
                                            <button
                                                key={tab.amount}
                                                className={cn(
                                                    "flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200 border",
                                                    tab.current
                                                        ? "bg-blue-600 text-white border-blue-500"
                                                        : tab.disabled
                                                        ? "bg-[#0d1b2a]/50 text-gray-500 border-[#2a4a7c] cursor-not-allowed"
                                                        : "bg-[#1a2f4a] text-gray-300 border-[#2a4a7c] hover:bg-[#1a2f4a]/80 hover:text-white"
                                                )}
                                                onClick={() => {
                                                    if (!tab.disabled) {
                                                        setCurrentHeartTab(tab.amount);
                                                    }
                                                }}
                                            >
                                                {tab.heart ? (
                                                    <div className="flex items-center justify-center">
                                                        {[...Array(tab.amount)].map((_, i) => (
                                                            <Heart
                                                                key={i}
                                                                className={cn(
                                                                    "h-5 w-5",
                                                                    tab.disabled
                                                                        ? "text-gray-500"
                                                                        : "text-pink-500 fill-pink-500"
                                                                )}
                                                            />
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <span className="text-lg font-semibold">All</span>
                                                )}
                                            </button>
                                        ))}
                                    </div>
                                </div>

                                {/* Limited Stock Info */}
                                {shopInfo?.id === 6 && (
                                    <div className="mb-4 space-y-3">
                                        <div className="p-3 rounded-lg border border-yellow-500/50 bg-yellow-500/10">
                                            <div className="flex items-center gap-2 text-yellow-400">
                                                <AlertCircle className="h-5 w-5" />
                                                <span>This shop contains limited stock.</span>
                                            </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            <div className="p-4 rounded-lg border border-[#2a4a7c] bg-[#1a2f4a]">
                                                <div className="text-sm text-gray-400 mb-1">Personal Buy Limit</div>
                                                <div className="text-2xl font-bold">
                                                    <span
                                                        className={cn(
                                                            currentUser?.weeklyBuyLimitRemaining > 0
                                                                ? "text-yellow-400"
                                                                : "text-red-400"
                                                        )}
                                                    >
                                                        {currentUser?.weeklyBuyLimitRemaining}
                                                    </span>
                                                    <span className="text-gray-400 text-lg">
                                                        /{LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="p-4 rounded-lg border border-[#2a4a7c] bg-[#1a2f4a]">
                                                <div className="text-sm text-gray-400 mb-1">Shop Closes In</div>
                                                <div className="text-2xl font-bold flex items-center gap-2">
                                                    <Clock className="h-5 w-5 text-blue-400" />
                                                    <CountdownTimer
                                                        showHours
                                                        targetDate={getNextSundayMidnight()}
                                                        showSeconds={false}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Shop Content */}
                                <div className="border-t border-[#2a4a7c] my-4"></div>
                                <div className="px-2">
                                    {sellOrBuyTab === "Buy" ? (
                                        <ShopBuyItems shopData={shopData} currentUser={currentUser} />
                                    ) : (
                                        <ShopSellItems currentUser={currentUser} />
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    );
}
