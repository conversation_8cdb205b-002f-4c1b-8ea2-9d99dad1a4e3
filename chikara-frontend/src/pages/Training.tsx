import { useState } from "react";
import ButtonSpinner from "@/components/Spinners/ButtonSpinner";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { <PERSON><PERSON><PERSON>, BookOpen, Target, Flame, Star, HelpCircle } from "lucide-react";
import useTrainStat from "@/features/user/training/useTrainStat";
import { useGetUserSkills, type SkillData } from "@/hooks/api/useGetUserSkills";
import { trainingStats } from "@/features/user/training/trainingConstants";
import TrainingResultModal from "@/components/Modal/TrainingResultModal";
import TrainingIntensitySelector from "@/features/user/training/TrainingIntensitySelector";
import type { UserStat } from "@/types/user";

export default function Training() {
    const { data: currentUser } = useFetchCurrentUser();
    const gameConfig = useGameConfig();
    const { data: userSkills } = useGetUserSkills([
        "strength",
        "intelligence",
        "dexterity",
        "defence",
        "endurance",
        "vitality",
    ]);

    // Individual training amounts for each stat
    const [trainingAmounts, setTrainingAmounts] = useState<Record<string, number>>({
        strength: 1,
        intelligence: 1,
        dexterity: 1,
        defence: 1,
        endurance: 1,
        vitality: 1,
    });

    // Modal states
    const [trainingResult, setTrainingResult] = useState<{
        result: any;
        statName: string;
    } | null>(null);
    const [trainingError, setTrainingError] = useState<string | null>(null);

    const { mutate: trainStat, isPending } = useTrainStat({
        onSuccess: (result, statName) => {
            setTrainingResult({ result, statName });
            setTrainingError(null);
        },
        onError: (error) => {
            setTrainingError(error);
            setTimeout(() => setTrainingError(null), 5000);
        },
    });

    // Get focus and daily fatigue info
    const currentFocus = currentUser?.focus || 0;
    const dailyFatigueCap = gameConfig?.DAILY_FATIGUE_CAP || 200;
    const dailyFatigueUsed = currentUser?.dailyFatigueUsed || 0;
    const dailyFatigueRemaining = dailyFatigueCap - dailyFatigueUsed;
    const focusToExpRatio = gameConfig?.FOCUS_TO_EXP_RATIO || 10;

    const handleTrainingAmountChange = (statId: string, value: number) => {
        const maxAllowed = Math.min(50, currentFocus, dailyFatigueRemaining);
        const numValue = Math.max(1, Math.min(maxAllowed, value));
        setTrainingAmounts((prev) => ({
            ...prev,
            [statId]: numValue,
        }));
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>, stat: string) => {
        e.preventDefault();
        const statName = stat.toLowerCase();
        trainStat({
            stat: statName as UserStat,
            focusAmount: trainingAmounts[statName] || 1,
        });
    };

    // Group stats by location
    const statsByLocation = {
        Gym: trainingStats.filter((s) => s.location === "Gym"),
        "Sports Hall": trainingStats.filter((s) => s.location === "Sports Hall"),
        Library: trainingStats.filter((s) => s.location === "Library"),
    };

    const locationIcons = {
        Gym: Dumbbell,
        "Sports Hall": Target,
        Library: BookOpen,
    };

    return (
        <div className="min-h-screen text-base-content">
            {/* Compact Header */}
            <div className="border-b border-base-300">
                <div className="max-w-7xl mx-auto px-4 py-4">
                    {/* Compact Stats Overview */}
                    <div className="grid grid-cols-6 gap-3">
                        {trainingStats.map((stat) => (
                            <div
                                key={stat.id}
                                className="card card-bordered bg-base-300 shadow-md hover:shadow-lg transition-shadow"
                            >
                                <div className="card-body p-3 items-center text-center">
                                    <stat.icon className="w-5 h-5 text-primary" />
                                    <div className="text-xs font-bold text-base-content">
                                        LV {userSkills?.[stat.id]?.level || "-"}
                                    </div>
                                    <div className="text-xs text-base-content/70">{stat.name}</div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            {/* Focus and Fatigue Display */}
            <div className="max-w-3xl mx-auto mt-4">
                {/* Combined Focus and Fatigue Display */}
                <div className="p-4 rounded-xl border border-base-300 bg-base-200 shadow-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Focus Section */}
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Star className="w-4 h-4 text-accent" />
                                <h3 className="text-sm font-semibold text-accent-content">Focus Points</h3>
                            </div>
                            <div className="flex items-baseline gap-2">
                                <div className="text-2xl font-bold text-accent">{currentFocus}</div>
                                <div className="text-xs text-accent-content">{focusToExpRatio} XP per focus</div>
                            </div>
                        </div>

                        {/* Fatigue Section */}
                        <div className="space-y-2">
                            <div className="flex items-center gap-2">
                                <Flame className="w-4 h-4 text-warning" />
                                <h3 className="text-sm font-semibold text-warning-content">Daily Fatigue</h3>
                            </div>
                            <div className="space-y-2">
                                <div className="flex items-baseline gap-2">
                                    <div className="text-lg font-bold text-warning-content">
                                        {dailyFatigueUsed} / {dailyFatigueCap}
                                    </div>
                                    <div className="text-xs text-warning-content">Resets at midnight</div>
                                </div>
                                <progress className="progress progress-warning w-full" value={dailyFatigueUsed} max={dailyFatigueCap}></progress>
                                {dailyFatigueUsed >= dailyFatigueCap * 0.8 && (
                                    <p className="text-xs text-error flex items-center gap-1">
                                        <HelpCircle className="w-3 h-3" />
                                        Daily training limit almost reached!
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Main Content - Responsive Grid */}
            <div className="max-w-7xl mx-auto px-4 py-6">
                <div className="grid xl:grid-cols-3 lg:grid-cols-2 grid-cols-1 gap-6">
                    {Object.entries(statsByLocation).map(([location, stats]) => {
                        const LocationIcon = locationIcons[location as keyof typeof locationIcons];
                        return (
                            <div key={location} className="space-y-4">
                                {/* Location Header */}
                                <div className="card bg-base-300 shadow-md hover:shadow-lg transition-shadow">
                                    <div className="card-body p-4 flex-row items-center gap-3">
                                        <LocationIcon className="w-6 h-6 text-secondary" />
                                        <div>
                                            <h2 className="card-title text-base text-base-content">{location}</h2>
                                            <p className="text-xs text-base-content/60">
                                                {location === "Gym" && "Physical training"}
                                                {location === "Sports Hall" && "Agility & endurance"}
                                                {location === "Library" && "Mental training"}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Stat Cards */}
                                <div className="space-y-3">
                                    {stats.map((stat) => (
                                        <StatCard
                                            key={stat.id}
                                            stat={stat}
                                            userSkill={userSkills?.[stat.id]}
                                            trainingAmount={trainingAmounts[stat.id] || 1}
                                            submit={handleSubmit}
                                            currentFocus={currentFocus}
                                            dailyFatigueRemaining={dailyFatigueRemaining}
                                            focusToExpRatio={focusToExpRatio}
                                            isLoading={isPending}
                                            trainingError={trainingError}
                                            onAmountChange={(value) => handleTrainingAmountChange(stat.id, value)}
                                        />
                                    ))}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Training Result Modal */}
            {trainingResult && (
                <TrainingResultModal
                    isOpen={!!trainingResult}
                    result={trainingResult.result}
                    statName={trainingResult.statName}
                    onClose={() => setTrainingResult(null)}
                />
            )}
        </div>
    );
}

interface StatCardProps {
    userSkill: SkillData | undefined;
    stat: {
        id: string;
        name: string;
        valueId: string;
        activity: string;
        location: string;
        image: string;
        icon: React.ElementType;
        effect: string;
        effect2?: string;
        color: string;
        bgColor: string;
        borderColor: string;
        accentColor: string;
        progressColor: string;
    };
    trainingAmount: number;
    onAmountChange: (_value: number) => void;
    submit: (_e: React.FormEvent<HTMLFormElement>, _stat: string) => void;
    currentFocus: number;
    dailyFatigueRemaining: number;
    focusToExpRatio: number;
    isLoading: boolean;
    trainingError: string | null;
}

const StatCard = ({
    userSkill,
    stat,
    trainingAmount,
    onAmountChange,
    submit,
    currentFocus,
    dailyFatigueRemaining,
    focusToExpRatio,
    isLoading,
    trainingError,
}: StatCardProps) => {
    if (!userSkill) return null;

    const progressPercentage =
        userSkill.level < 100 ? (userSkill.experience / (userSkill.experience + userSkill.expToNextLevel)) * 100 : 100;
    const focusCost = trainingAmount;
    const expGain = focusCost * focusToExpRatio;
    const notEnoughFocus = currentFocus < focusCost;
    const notEnoughDailyCapacity = dailyFatigueRemaining < focusCost;

    return (
        <div className="card bg-base-200 shadow-xl hover:shadow-2xl transition-all duration-300">
            <div className="card-body p-4">
                {/* Header */}
                <div className="flex items-center gap-3 mb-3">
                    <div className="avatar">
                        <div className="w-10 h-10 rounded-full bg-primary/20 p-1">
                            <img src={stat.image} className="w-full h-full object-contain" alt={stat.name} />
                        </div>
                    </div>
                    <div className="flex-1">
                        <h3 className="text-base font-bold text-primary">{stat.name}</h3>
                        <p className="text-xs text-base-content/70">{stat.activity}</p>
                    </div>
                    <div className="badge badge-primary badge-outline">
                        <span className="text-xs font-bold">
                            {userSkill.level >= 100 ? "MAX" : `LV ${userSkill.level}`}
                        </span>
                    </div>
                </div>

                {/* Stat Benefits */}
                <div className="alert alert-info alert-soft p-2 mb-3">
                    <div className="flex items-start gap-1.5">
                        <stat.icon className="w-4 h-4 text-info flex-shrink-0" />
                        <div className="space-y-0.5">
                            <p className="text-xs font-medium">{stat.effect}</p>
                            {stat.effect2 && <p className="text-xs opacity-80">{stat.effect2}</p>}
                        </div>
                    </div>
                </div>

                {/* Progress Bar */}
                {userSkill.level < 100 && (
                    <div className="mb-3">
                        <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-base-content/60">
                                {userSkill.experience}/{userSkill.experience + userSkill.expToNextLevel} XP
                            </span>
                            <span className="text-xs font-bold text-accent">+{expGain} XP</span>
                        </div>
                        <div className="relative">
                            <progress 
                                className="progress progress-primary w-full" 
                                value={progressPercentage} 
                                max="100"
                            ></progress>
                            {/* Preview overlay */}
                            {(() => {
                                const previewExpGain = trainingAmount * focusToExpRatio;
                                const totalExpAfterTraining = userSkill.experience + previewExpGain;
                                const maxExpForLevel = userSkill.experience + userSkill.expToNextLevel;
                                const previewProgressPercentage = Math.min(
                                    100,
                                    (totalExpAfterTraining / maxExpForLevel) * 100
                                );

                                if (previewProgressPercentage > progressPercentage) {
                                    return (
                                        <div
                                            className="absolute top-0 h-full bg-accent/50 rounded-full"
                                            style={{
                                                left: `${progressPercentage}%`,
                                                width: `${Math.min(100 - progressPercentage, previewProgressPercentage - progressPercentage)}%`,
                                            }}
                                        />
                                    );
                                }
                                return null;
                            })()}
                        </div>
                    </div>
                )}

                {/* Training Controls */}
                {userSkill.level < 100 && (
                    <div className="space-y-3">
                        {/* Intensity Control */}
                        <TrainingIntensitySelector
                            value={trainingAmount}
                            valueId={stat.valueId}
                            onChange={onAmountChange}
                        />

                        {/* Error Alert */}
                        {trainingError && (
                            <div className="alert alert-error">
                                <p className="text-xs">{trainingError}</p>
                            </div>
                        )}

                        {/* Train Button */}
                        <form onSubmit={(e) => submit(e, stat.name)}>
                            <button
                                type="submit"
                                disabled={isLoading || notEnoughFocus || notEnoughDailyCapacity}
                                className={`btn btn-sm w-full ${
                                    notEnoughFocus
                                        ? "btn-warning btn-outline"
                                        : notEnoughDailyCapacity
                                          ? "btn-error btn-outline"
                                          : "btn-primary"
                                }`}
                            >
                                {isLoading ? (
                                    <>
                                        <span className="loading loading-spinner loading-xs"></span>
                                        <span>Training...</span>
                                    </>
                                ) : notEnoughFocus ? (
                                    <>
                                        <Star className="w-4 h-4" />
                                        <span>Need {focusCost} focus</span>
                                    </>
                                ) : notEnoughDailyCapacity ? (
                                    <>
                                        <Flame className="w-4 h-4" />
                                        <span>Daily limit reached</span>
                                    </>
                                ) : (
                                    <>
                                        <Dumbbell className="w-4 h-4" />
                                        <span>Train ({focusCost} focus)</span>
                                    </>
                                )}
                            </button>
                        </form>
                    </div>
                )}
            </div>
        </div>
    );
};
